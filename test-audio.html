<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Audio Test</title>
</head>
<body>
    <h1>Audio Test für Tennet Geschichte</h1>
    
    <h2>Direkte Audio-Tests:</h2>
    
    <h3>Test 1: Direkter Pfad</h3>
    <audio controls>
        <source src="audio/kindergeschichten/Tennet und die magische Blume.m4a" type="audio/mp4">
        Ihr Browser unterstützt das Audio-Element nicht.
    </audio>
    
    <h3>Test 2: JavaScript Audio (ohne Encoding)</h3>
    <button onclick="testAudio()">Audio mit JavaScript testen</button>
    <div id="result"></div>

    <h3>Test 2b: JavaScript Audio (mit URL-Encoding)</h3>
    <button onclick="testAudioEncoded()">Audio mit URL-Encoding testen</button>
    <div id="resultEncoded"></div>
    
    <h3>Test 3: Pfad-Überprüfung</h3>
    <button onclick="checkPath()">Pfad überprüfen</button>
    <div id="pathResult"></div>

    <h3>Test 4: Browser-Unterstützung</h3>
    <button onclick="checkBrowserSupport()">Browser-Unterstützung prüfen</button>
    <div id="supportResult"></div>
    
    <script>
        function testAudio() {
            const audio = new Audio();
            const resultDiv = document.getElementById('result');
            
            audio.addEventListener('loadstart', () => {
                resultDiv.innerHTML += '<p>✓ loadstart - Laden gestartet</p>';
            });
            
            audio.addEventListener('loadeddata', () => {
                resultDiv.innerHTML += '<p>✓ loadeddata - Erste Daten geladen</p>';
            });
            
            audio.addEventListener('loadedmetadata', () => {
                resultDiv.innerHTML += '<p>✓ loadedmetadata - Metadaten geladen, Dauer: ' + audio.duration + 's</p>';
            });
            
            audio.addEventListener('canplay', () => {
                resultDiv.innerHTML += '<p>✓ canplay - Kann abgespielt werden</p>';
            });
            
            audio.addEventListener('error', (e) => {
                resultDiv.innerHTML += '<p style="color: red;">✗ Fehler: ' + e.type + '</p>';
                if (audio.error) {
                    resultDiv.innerHTML += '<p style="color: red;">Error Code: ' + audio.error.code + '</p>';
                    resultDiv.innerHTML += '<p style="color: red;">Error Message: ' + audio.error.message + '</p>';
                }
            });
            
            resultDiv.innerHTML = '<p>Teste Audio-Datei...</p>';
            audio.src = 'audio/kindergeschichten/Tennet und die magische Blume.m4a';
            audio.load();
        }

        function testAudioEncoded() {
            const audio = new Audio();
            const resultDiv = document.getElementById('resultEncoded');

            audio.addEventListener('loadstart', () => {
                resultDiv.innerHTML += '<p>✓ loadstart - Laden gestartet</p>';
            });

            audio.addEventListener('loadeddata', () => {
                resultDiv.innerHTML += '<p>✓ loadeddata - Erste Daten geladen</p>';
            });

            audio.addEventListener('loadedmetadata', () => {
                resultDiv.innerHTML += '<p>✓ loadedmetadata - Metadaten geladen, Dauer: ' + audio.duration + 's</p>';
            });

            audio.addEventListener('canplay', () => {
                resultDiv.innerHTML += '<p>✓ canplay - Kann abgespielt werden</p>';
            });

            audio.addEventListener('error', (e) => {
                resultDiv.innerHTML += '<p style="color: red;">✗ Fehler: ' + e.type + '</p>';
                if (audio.error) {
                    resultDiv.innerHTML += '<p style="color: red;">Error Code: ' + audio.error.code + '</p>';
                    resultDiv.innerHTML += '<p style="color: red;">Error Message: ' + audio.error.message + '</p>';
                }
            });

            const filename = 'Tennet und die magische Blume.m4a';
            const encodedPath = 'audio/kindergeschichten/' + encodeURIComponent(filename);

            resultDiv.innerHTML = '<p>Teste Audio-Datei mit URL-Encoding...</p>';
            resultDiv.innerHTML += '<p>Original: ' + filename + '</p>';
            resultDiv.innerHTML += '<p>Encoded: ' + encodeURIComponent(filename) + '</p>';
            resultDiv.innerHTML += '<p>Full path: ' + encodedPath + '</p>';

            audio.src = encodedPath;
            audio.load();
        }
        
        function checkPath() {
            const pathDiv = document.getElementById('pathResult');
            const testPath = 'audio/kindergeschichten/Tennet und die magische Blume.m4a';
            
            fetch(testPath, { method: 'HEAD' })
                .then(response => {
                    if (response.ok) {
                        pathDiv.innerHTML = '<p style="color: green;">✓ Datei ist erreichbar (Status: ' + response.status + ')</p>';
                        pathDiv.innerHTML += '<p>Content-Type: ' + response.headers.get('content-type') + '</p>';
                        pathDiv.innerHTML += '<p>Content-Length: ' + response.headers.get('content-length') + ' bytes</p>';
                    } else {
                        pathDiv.innerHTML = '<p style="color: red;">✗ Datei nicht erreichbar (Status: ' + response.status + ')</p>';
                    }
                })
                .catch(error => {
                    pathDiv.innerHTML = '<p style="color: red;">✗ Fehler beim Zugriff: ' + error.message + '</p>';
                });
        }
        
        function checkBrowserSupport() {
            const supportDiv = document.getElementById('supportResult');
            const audio = document.createElement('audio');

            const formats = [
                { type: 'audio/mp4', ext: 'm4a', desc: 'M4A (AAC in MP4)' },
                { type: 'audio/mpeg', ext: 'mp3', desc: 'MP3' },
                { type: 'audio/wav', ext: 'wav', desc: 'WAV' },
                { type: 'audio/ogg', ext: 'ogg', desc: 'OGG' }
            ];

            let result = '<h4>Audio-Format-Unterstützung:</h4>';

            formats.forEach(format => {
                const support = audio.canPlayType(format.type);
                const color = support === 'probably' ? 'green' :
                             support === 'maybe' ? 'orange' : 'red';
                result += `<p style="color: ${color};">${format.desc} (${format.ext}): ${support || 'nicht unterstützt'}</p>`;
            });

            // Spezielle Tests für M4A-Varianten
            const m4aTests = [
                'audio/mp4',
                'audio/mp4; codecs="mp4a.40.2"',
                'audio/x-m4a',
                'audio/aac'
            ];

            result += '<h4>M4A-spezifische Tests:</h4>';
            m4aTests.forEach(type => {
                const support = audio.canPlayType(type);
                const color = support === 'probably' ? 'green' :
                             support === 'maybe' ? 'orange' : 'red';
                result += `<p style="color: ${color};">${type}: ${support || 'nicht unterstützt'}</p>`;
            });

            supportDiv.innerHTML = result;
        }

        // Automatischer Test beim Laden
        window.addEventListener('load', () => {
            console.log('Seite geladen, starte automatischen Test...');
            setTimeout(() => {
                checkPath();
                checkBrowserSupport();
            }, 1000);
        });
    </script>
</body>
</html>
