<!DOCTYPE html>
<html>
<head>
    <title>Test Kindergeschichten Data</title>
</head>
<body>
    <h1>Test Kindergeschichten Data</h1>
    <div id="output"></div>
    
    <script src="kindergeschichten-data.js"></script>
    <script>
        const output = document.getElementById('output');
        
        if (typeof KINDERGESCHICHTEN_DATEN !== 'undefined') {
            output.innerHTML = `
                <p style="color: green;">✅ KINDERGESCHICHTEN_DATEN ist definiert!</p>
                <p>Anzahl Geschichten: ${KINDERGESCHICHTEN_DATEN.length}</p>
                <pre>${JSON.stringify(KINDERGESCHICHTEN_DATEN, null, 2)}</pre>
            `;
        } else {
            output.innerHTML = `<p style="color: red;">❌ KINDERGESCHICHTEN_DATEN ist NICHT definiert!</p>`;
        }
        
        if (typeof KINDERGESCHICHTEN_KONFIGURATION !== 'undefined') {
            output.innerHTML += `
                <p style="color: green;">✅ KINDERGESCHICHTEN_KONFIGURATION ist definiert!</p>
                <pre>${JSON.stringify(KINDERGESCHICHTEN_KONFIGURATION, null, 2)}</pre>
            `;
        } else {
            output.innerHTML += `<p style="color: red;">❌ KINDERGESCHICHTEN_KONFIGURATION ist NICHT definiert!</p>`;
        }
    </script>
</body>
</html>
