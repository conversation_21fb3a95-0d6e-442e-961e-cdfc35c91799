/**
 * Kindergeschichten Player JavaScript
 * Hauptfunktionalität für die Kindergeschichten-Seite
 */

class KindergeschichtenPlayer {
    constructor() {
        this.stories = [];
        this.currentStoryIndex = -1;
        this.isPlaying = false;
        this.isShuffleMode = false;
        this.repeatMode = 'none'; // 'none', 'playlist', 'single'
        this.currentSort = { column: 'nummer', direction: 'asc' };
        this.hiddenStories = new Set();
        this.filteredStories = [];

        // Shuffle-Verwaltung
        this.shuffleHistory = [];
        this.shuffleQueue = [];

        // Warteschlange (Queue)
        this.playQueue = [];

        // Filter-Zustand
        this.selectedFilters = {
            kategorie: new Set(),
            sprache: new Set(),
            altersgruppe: new Set()
        };

        // Media Session
        this.mediaSessionHandlersSet = false;
        this.currentCoverPath = null;

        // Modal Event Listeners
        this.modalEventListenersSet = false;

        // Desktop Event Listeners
        this.desktopEventListenersSet = false;
        
        // Audio Element
        this.audio = document.getElementById('audio-player');

        // Debouncing für Story-Auswahl
        this.storySelectionTimeout = null;
        this.isChangingStory = false;

        // Debouncing für Skip-Buttons - moderates Cooldown
        this.skipDebounceTimeout = null;
        this.isSkipping = false;
        this.lastSkipTime = 0;
        this.skipCooldown = 200; // 200ms Cooldown - verhindert Spam aber erlaubt schnelles Skippen

        // Previous-Button Verhalten (Neustart vs. vorheriges Geschichte)
        this.lastPreviousClickTime = 0;
        this.previousClickThreshold = 3000; // 3 Sekunden - wenn Story länger als 3s läuft, wird neugestartet

        // DOM Elements
        this.mobileStoryList = document.getElementById('mobile-story-list');

        // Mobile Elements
        this.miniPlayer = document.getElementById('mini-player');
        this.miniStoryTitle = document.getElementById('mini-story-title');
        this.miniPlayPause = document.getElementById('mini-play-pause');
        this.modalOverlay = document.getElementById('modal-overlay');
        this.modalPlayer = document.getElementById('modal-player');
    }

    initialize() {
        this.init();
    }

    init() {
        console.log('KindergeschichtenPlayer init() gestartet');
        this.loadStories();
        console.log('Nach loadStories - stories:', this.stories.length);
        this.setupEventListeners();
        this.setupFilters();
        this.renderMobileStoryList();
        this.updateRepeatButtons(); // Initial button state setzen

        // Desktop Sidebar initialisieren wenn auf Desktop
        if (this.isDesktop()) {
            this.initializeDesktopSidebar();
        }

        // URL-Parameter prüfen für automatisches Abspielen
        this.checkUrlParameters();
        console.log('KindergeschichtenPlayer init() abgeschlossen');
    }

    loadStories() {
        if (typeof KINDERGESCHICHTEN_DATEN === 'undefined') {
            console.error('KINDERGESCHICHTEN_DATEN ist nicht definiert');
            this.stories = [];
            this.filteredStories = [];
            return;
        }

        // Stories aus kindergeschichten-data.js laden und mit Index versehen
        this.stories = KINDERGESCHICHTEN_DATEN.map((story, index) => ({
            ...story,
            id: index + 1,
            nummer: index + 1,
            isHidden: false
        }));

        // Gefilterte Stories initial auf alle Stories setzen
        this.filteredStories = [...this.stories];

        console.log('Stories geladen:', this.stories.length, this.stories);
    }
    
    setupEventListeners() {
        // Audio Events
        this.audio.addEventListener('loadedmetadata', () => {
            this.updateDuration();
            this.updateMediaSessionPosition();
        });
        this.audio.addEventListener('timeupdate', () => {
            this.updateProgress();
            this.updateModalProgressIfOpen();
            this.updateMediaSessionPosition();
        });
        this.audio.addEventListener('ended', () => {
            console.log('Audio ended, checking if auto-advance is appropriate...');
            // Nur zum nächsten Story wenn nicht bereits ein Skip im Gange ist
            if (!this.isSkipping) {
                console.log('Audio ended naturally, advancing to next story');
                this.nextStory();
            } else {
                console.log('Skip already in progress, ignoring ended event');
            }
        });
        this.audio.addEventListener('error', (e) => this.handleAudioError(e));
        this.audio.addEventListener('play', () => {
            this.isPlaying = true;
            this.updatePlayButton();
            this.updateMediaSessionPlaybackState();
        });
        this.audio.addEventListener('pause', () => {
            this.isPlaying = false;
            this.updatePlayButton();
            this.updateMediaSessionPlaybackState();
        });
        
        // Player Controls (nur wenn Elemente existieren)
        const playPauseBtn = document.getElementById('play-pause-btn');
        const prevBtn = document.getElementById('prev-btn');
        const nextBtn = document.getElementById('next-btn');
        const shuffleBtn = document.getElementById('shuffle-btn');
        const repeatBtn = document.getElementById('repeat-btn');

        if (playPauseBtn) playPauseBtn.addEventListener('click', () => this.togglePlayPause());
        if (prevBtn) prevBtn.addEventListener('click', () => this.previousStory());
        if (nextBtn) nextBtn.addEventListener('click', () => this.nextStory());
        if (shuffleBtn) shuffleBtn.addEventListener('click', () => this.toggleShuffle());
        if (repeatBtn) repeatBtn.addEventListener('click', () => this.toggleRepeat());
        
        // Progress Bar (nur wenn Element existiert)
        const progressContainer = document.querySelector('.progress-bar-container');
        if (progressContainer) {
            progressContainer.addEventListener('click', (e) => this.seekTo(e));
        }

        // Table Sorting (nur wenn Elemente existieren)
        document.querySelectorAll('.sortable').forEach(header => {
            header.addEventListener('click', () => this.sortTable(header.dataset.column));
        });
        
        // Filter Controls werden jetzt in setupSimpleFilters() behandelt

        // Lyrics Tabs (nur wenn Elemente existieren)
        const lyricsTabs = document.querySelectorAll('.lyrics-tab');
        if (lyricsTabs.length > 0) {
            this.setupLyricsTabs();
        }



        // Mobile Filter Toggle
        const mobileFilterToggle = document.getElementById('mobile-filter-toggle');
        if (mobileFilterToggle) {
            mobileFilterToggle.addEventListener('click', () => this.toggleMobileFilters());
        }

        // Mobile Control Buttons
        const mobilePlayFilteredBtn = document.getElementById('mobile-play-filtered-btn');
        const mobilePrevBtn = document.getElementById('mobile-prev-btn');
        const mobileNextBtn = document.getElementById('mobile-next-btn');
        const mobileShuffleBtn = document.getElementById('mobile-shuffle-btn');
        const mobileRepeatBtn = document.getElementById('mobile-repeat-btn');
        const mobileDownloadFilteredBtn = document.getElementById('mobile-download-filtered-btn');
        const filterStatusResetBtn = document.getElementById('filter-status-reset');

        if (mobilePlayFilteredBtn) mobilePlayFilteredBtn.addEventListener('click', () => this.togglePlayPauseWithDefault());
        if (mobilePrevBtn) mobilePrevBtn.addEventListener('click', () => this.previousStory());
        if (mobileNextBtn) mobileNextBtn.addEventListener('click', () => this.nextStory());
        if (mobileShuffleBtn) mobileShuffleBtn.addEventListener('click', () => this.toggleShuffle());
        if (mobileRepeatBtn) mobileRepeatBtn.addEventListener('click', () => this.toggleRepeat());
        if (mobileDownloadFilteredBtn) mobileDownloadFilteredBtn.addEventListener('click', () => this.downloadFilteredList());
        if (filterStatusResetBtn) filterStatusResetBtn.addEventListener('click', () => this.resetFilters());
        
        // Header Actions (nur wenn Elemente existieren)
        const clearFiltersBtn = document.getElementById('clear-filters');
        const downloadCurrentBtn = document.getElementById('download-current-btn');

        if (clearFiltersBtn) clearFiltersBtn.addEventListener('click', () => this.clearFiltersAndQueue());
        if (downloadCurrentBtn) downloadCurrentBtn.addEventListener('click', () => this.downloadCurrent());

        // Close menus when clicking outside
        document.addEventListener('click', (e) => {
            // Nur schließen wenn nicht auf Menü oder Menü-Button geklickt wurde
            if (!e.target.closest('.mobile-story-menu') && !e.target.closest('.mobile-menu-dropdown')) {
                this.closeMobileMenus();
            }
        });
        
        // Mobile Events
        if (this.miniPlayPause) {
            this.miniPlayPause.addEventListener('click', () => {
                this.togglePlayPause();
            });
        }

        const miniExpandBtn = document.getElementById('mini-expand');
        if (miniExpandBtn) {
            miniExpandBtn.addEventListener('click', () => {
                this.openMobilePlayer();
            });
        }

        const miniPlayerInfo = document.getElementById('mini-player-info');
        if (miniPlayerInfo) {
            miniPlayerInfo.addEventListener('click', () => {
                this.openMobilePlayer();
            });
        }

        const modalCloseBtn = document.getElementById('modal-close');
        if (modalCloseBtn) {
            modalCloseBtn.addEventListener('click', () => this.closeMobilePlayer());
        }

        if (this.modalOverlay) {
            this.modalOverlay.addEventListener('click', (e) => {
                if (e.target === this.modalOverlay) this.closeMobilePlayer();
            });
        }
        
        // Keyboard Shortcuts
        document.addEventListener('keydown', (e) => this.handleKeyboard(e));

        // Window Resize für Mini-Player
        window.addEventListener('resize', () => this.handleResize());
    }
    

    
    renderMobileStoryList() {
        console.log('renderMobileStoryList aufgerufen');

        // Element direkt suchen
        const storyListElement = document.getElementById('mobile-story-list');
        console.log('Story List Element gefunden:', storyListElement);
        console.log('filteredStories:', this.filteredStories);

        if (!storyListElement) {
            console.error('mobile-story-list Element nicht gefunden!');
            return;
        }

        if (!this.filteredStories || this.filteredStories.length === 0) {
            console.error('Keine filteredStories vorhanden!');
            storyListElement.innerHTML = '<p style="padding: 20px; text-align: center;">Keine Geschichten gefunden.</p>';
            return;
        }

        storyListElement.innerHTML = '';

        this.filteredStories.forEach((story) => {
            if (story.isHidden) return;

            const item = document.createElement('div');
            item.className = 'mobile-story-item';
            item.dataset.storyId = story.id;

            // Dynamische Anzeige basierend auf aktiven Filtern
            const dynamicInfo = this.getDynamicStoryInfo(story);

            item.innerHTML = `
                <div class="mobile-story-content">
                    <div class="mobile-story-cover" data-story-number="${story.nummer}">
                        <svg class="placeholder-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                        </svg>
                    </div>
                    <div class="mobile-story-info">
                        <div class="mobile-story-title">${story.titel}</div>
                        <div class="mobile-story-meta">
                            <span class="mobile-story-category">${story.kategorie}</span>
                            <span class="mobile-story-age">${story.altersgruppe}</span>
                        </div>
                        <div class="mobile-story-description">${story.kurzbeschreibung}</div>
                    </div>
                    <div class="mobile-story-actions">
                        <button class="mobile-story-play-btn" data-story-id="${story.id}" title="Geschichte abspielen">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M8 5v14l11-7z"/>
                            </svg>
                        </button>
                        <button class="mobile-story-menu-btn" data-story-id="${story.id}" title="Aktionen">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <circle cx="12" cy="12" r="1"/>
                                <circle cx="12" cy="5" r="1"/>
                                <circle cx="12" cy="19" r="1"/>
                            </svg>
                        </button>
                        <div class="mobile-story-menu" data-story-id="${story.id}">
                            <button class="mobile-story-menu-item" data-action="download" data-story-id="${story.id}">
                                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                                    <polyline points="7,10 12,15 17,10"/>
                                    <line x1="12" y1="15" x2="12" y2="3"/>
                                </svg>
                                <span>PDF herunterladen</span>
                            </button>
                        </div>
                    </div>
                </div>
            `;

            // Story-Item Click (nur wenn nicht auf Menü geklickt)
            item.addEventListener('click', (e) => {
                if (!e.target.closest('.mobile-story-menu')) {
                    this.safeMobileStorySelection(story.id - 1);
                }
            });

            // Play Button Event Listener
            const playBtn = item.querySelector('.mobile-story-play-btn');
            if (playBtn) {
                playBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.safeMobileStorySelection(story.id - 1);
                });
            }

            // Menü Button Event Listener
            const menuBtn = item.querySelector('.mobile-story-menu-btn');
            if (menuBtn) {
                menuBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    this.toggleMobileMenu(story.id);
                });
            }

            // Menü Item Event Listeners
            const menuItems = item.querySelectorAll('.mobile-story-menu-item');
            menuItems.forEach(menuItem => {
                menuItem.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const action = menuItem.dataset.action;
                    const storyId = parseInt(menuItem.dataset.storyId);
                    this.handleMobileMenuAction(action, storyId);
                    this.closeMobileMenus();
                });
            });

            // Cover-Element ist bereits im HTML mit SVG-Platzhalter

            storyListElement.appendChild(item);
        });

        console.log('renderMobileStoryList abgeschlossen, Anzahl Stories:', this.filteredStories.length);
    }
    
    setupFilters() {
        // Eindeutige Werte für alle Filter sammeln
        const kategories = [...new Set(this.stories.map(story => story.kategorie))].sort();
        const sprachen = [...new Set(this.stories.map(story => story.sprache))].sort();
        const altersgruppen = [...new Set(this.stories.map(story => story.altersgruppe))].sort();

        // Alle Filter initial als ausgewählt setzen (zeigt alle Geschichten)
        this.selectedFilters.kategorie = new Set(kategories);
        this.selectedFilters.sprache = new Set(sprachen);
        this.selectedFilters.altersgruppe = new Set(altersgruppen);

        // Multiple Choice Filter erstellen
        this.createFilterOptions('kategorie', kategories, false);
        this.createFilterOptions('sprache', sprachen, false);
        this.createFilterOptions('kategorie', kategories, false);

        // Mobile Filter erstellen
        this.createFilterOptions('kategorie', kategories, true);
        this.createFilterOptions('sprache', sprachen, true);
        this.createFilterOptions('kategorie', kategories, true);

        // Event Listeners für Filter-Dropdowns mit Delay
        setTimeout(() => {
            this.setupFilterEventListeners();
        }, 100);

        // Initial alle Checkboxen aktivieren und Button-Texte setzen
        setTimeout(() => {
            this.updateAllFilterCheckboxes();
            // Zusätzliche Synchronisation für "Alle [Kategorie]" Checkboxen
            setTimeout(() => {
                this.syncAllCategoryCheckboxes();
            }, 100);
        }, 200);

        // Backup Event Listener direkt setzen
        setTimeout(() => {
            const btn = document.getElementById('apply-filters-btn');
            const mobileBtn = document.getElementById('mobile-apply-filters-btn');

            if (btn) {
                btn.onclick = () => {
                    this.applyFilters();
                };
            }

            if (mobileBtn) {
                mobileBtn.onclick = () => {
                    this.applyFilters();
                };
            }
        }, 500);


    }

    createFilterOptions(filterType, options, isMobile) {
        const prefix = isMobile ? 'mobile-' : '';
        const optionsContainer = document.getElementById(`${prefix}${filterType}-filter-options`);

        if (!optionsContainer) {
            return;
        }

        optionsContainer.innerHTML = '';

        // "Alle [Kategorie]" Toggle-Button hinzufügen
        const allOptionDiv = document.createElement('div');
        allOptionDiv.className = 'filter-option filter-all-option';
        allOptionDiv.addEventListener('click', (e) => e.stopPropagation());

        const allCheckbox = document.createElement('input');
        allCheckbox.type = 'checkbox';
        allCheckbox.id = `${prefix}${filterType}-alle`;
        allCheckbox.value = 'alle';
        allCheckbox.checked = true; // Initial alle ausgewählt (wird später synchronisiert)
        allCheckbox.addEventListener('change', (e) => {
            e.stopPropagation();
            this.handleAllFilterToggle(filterType, e.target.checked, isMobile);
        });

        const allLabel = document.createElement('label');
        allLabel.htmlFor = allCheckbox.id;
        allLabel.textContent = `Alle ${this.getFilterDisplayName(filterType)}`;
        allLabel.className = 'filter-all-label';
        allLabel.addEventListener('click', (e) => e.stopPropagation());

        allOptionDiv.appendChild(allCheckbox);
        allOptionDiv.appendChild(allLabel);
        optionsContainer.appendChild(allOptionDiv);

        // Trennlinie hinzufügen
        const separator = document.createElement('div');
        separator.className = 'filter-separator';
        optionsContainer.appendChild(separator);

        // Einzelne Optionen hinzufügen
        options.forEach(option => {
            const optionDiv = document.createElement('div');
            optionDiv.className = 'filter-option';
            optionDiv.addEventListener('click', (e) => e.stopPropagation());

            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.id = `${prefix}${filterType}-${option.toLowerCase().replace(/\s+/g, '-')}`;
            checkbox.value = option;
            checkbox.checked = true; // Initial alle ausgewählt
            checkbox.addEventListener('change', (e) => {
                e.stopPropagation();
                this.handleFilterChange(filterType, option, e.target.checked);
            });

            const label = document.createElement('label');
            label.htmlFor = checkbox.id;
            label.textContent = option;
            label.addEventListener('click', (e) => e.stopPropagation());

            optionDiv.appendChild(checkbox);
            optionDiv.appendChild(label);
            optionsContainer.appendChild(optionDiv);
        });
    }

    setupFilterEventListeners() {
        // Dropdown-Buttons
        const filterTypes = ['kategorie', 'sprache', 'kategorie'];
        const prefixes = ['', 'mobile-'];

        prefixes.forEach(prefix => {
            filterTypes.forEach(filterType => {
                const btn = document.getElementById(`${prefix}${filterType}-filter-btn`);
                const dropdown = document.getElementById(`${prefix}${filterType}-filter-dropdown`);

                if (btn && dropdown) {
                    btn.addEventListener('click', (e) => {
                        e.stopPropagation();
                        this.toggleFilterDropdown(dropdown);
                    });
                } else {
                    console.warn(`Filter elements not found: ${prefix}${filterType}-filter-btn or ${prefix}${filterType}-filter-dropdown`);
                }
            });
        });



        // Filter Anwenden Buttons
        const applyFiltersBtn = document.getElementById('apply-filters-btn');
        if (applyFiltersBtn) {
            applyFiltersBtn.addEventListener('click', () => {
                this.closeAllFilterDropdowns(false);
                this.applyFilters();
            });
        }

        const mobileApplyFiltersBtn = document.getElementById('mobile-apply-filters-btn');
        if (mobileApplyFiltersBtn) {
            mobileApplyFiltersBtn.addEventListener('click', () => {
                this.closeAllFilterDropdowns(false);
                this.applyFilters();
            });
        }

        // Reset-Buttons Event Listener
        const clearFiltersBtn = document.getElementById('clear-filters');
        if (clearFiltersBtn) {
            clearFiltersBtn.addEventListener('click', () => this.clearFiltersAndQueue());
        }

        const mobileResetBtn = document.getElementById('mobile-reset-filters');
        if (mobileResetBtn) {
            mobileResetBtn.addEventListener('click', () => this.clearFiltersAndQueue());
        }

        // Dropdown schließen wenn außerhalb geklickt wird (OHNE Filter anzuwenden)
        document.addEventListener('click', () => {
            this.closeAllFilterDropdowns(false);
        });
    }

    handleFilterChange(filterType, option, isChecked) {
        if (isChecked) {
            this.selectedFilters[filterType].add(option);
        } else {
            this.selectedFilters[filterType].delete(option);
        }

        // "Alle [Kategorie]" Checkbox aktualisieren
        this.updateAllFilterCheckbox(filterType);

        this.updateFilterButtonText(filterType);

        // Filter werden nur noch über den "Filter anwenden" Button angewendet
    }

    handleAllFilterToggle(filterType, isChecked, isMobile) {
        const prefix = isMobile ? 'mobile-' : '';
        const optionsContainer = document.getElementById(`${prefix}${filterType}-filter-options`);

        if (!optionsContainer) return;

        // Alle einzelnen Checkboxen (außer der "Alle" Checkbox) finden
        const individualCheckboxes = optionsContainer.querySelectorAll('input[type="checkbox"]:not([value="alle"])');

        if (isChecked) {
            // Alle auswählen
            individualCheckboxes.forEach(checkbox => {
                checkbox.checked = true;
                this.selectedFilters[filterType].add(checkbox.value);
            });
        } else {
            // Alle abwählen
            individualCheckboxes.forEach(checkbox => {
                checkbox.checked = false;
                this.selectedFilters[filterType].delete(checkbox.value);
            });
        }

        // Auch die andere Plattform (Desktop/Mobile) synchronisieren
        this.syncAllFilterCheckboxes(filterType, isChecked);

        this.updateFilterButtonText(filterType);

        // Filter werden nur noch über den "Filter anwenden" Button angewendet
    }

    updateAllFilterCheckbox(filterType) {
        const prefixes = ['', 'mobile-'];

        prefixes.forEach(prefix => {
            const optionsContainer = document.getElementById(`${prefix}${filterType}-filter-options`);

            if (!optionsContainer) return;

            const allCheckbox = optionsContainer.querySelector('input[value="alle"]');
            const individualCheckboxes = optionsContainer.querySelectorAll('input[type="checkbox"]:not([value="alle"])');

            if (allCheckbox && individualCheckboxes.length > 0) {
                const allChecked = Array.from(individualCheckboxes).every(cb => cb.checked);
                allCheckbox.checked = allChecked;
            }
        });
    }

    syncAllFilterCheckboxes(filterType, isChecked) {
        const prefixes = ['', 'mobile-'];

        prefixes.forEach(prefix => {
            const optionsContainer = document.getElementById(`${prefix}${filterType}-filter-options`);
            if (!optionsContainer) return;

            const allCheckbox = optionsContainer.querySelector('input[value="alle"]');
            const individualCheckboxes = optionsContainer.querySelectorAll('input[type="checkbox"]:not([value="alle"])');

            if (allCheckbox) {
                allCheckbox.checked = isChecked;
            }

            individualCheckboxes.forEach(checkbox => {
                checkbox.checked = isChecked;
            });
        });
    }



    updateFilterButtonText(filterType) {
        const selectedCount = this.selectedFilters[filterType].size;
        const totalOptionsElement = document.querySelector(`#${filterType}-filter-options`);
        const totalOptions = totalOptionsElement ?
            totalOptionsElement.querySelectorAll('input[type="checkbox"]').length : 0;

        let text;
        if (selectedCount === 0) {
            text = `Keine ${this.getFilterDisplayName(filterType)}`;
        } else if (selectedCount === totalOptions && totalOptions > 0) {
            text = `Alle ${this.getFilterDisplayName(filterType)}`;
        } else {
            text = `${selectedCount} ${this.getFilterDisplayName(filterType)}`;
        }

        // Desktop Button aktualisieren
        const desktopBtn = document.getElementById(`${filterType}-filter-btn`);
        if (desktopBtn) {
            const textElement = desktopBtn.querySelector('.filter-text');
            if (textElement) {
                textElement.textContent = text;
            }
        }

        // Mobile Button aktualisieren
        const mobileBtn = document.getElementById(`mobile-${filterType}-filter-btn`);
        if (mobileBtn) {
            const textElement = mobileBtn.querySelector('.filter-text');
            if (textElement) {
                textElement.textContent = text;
            }
        }
    }

    getFilterDisplayName(filterType) {
        const displayNames = {
            kategorie: 'Kategories',
            sprache: 'Sprachen',
            kategorie: 'Alben'
        };
        return displayNames[filterType] || filterType;
    }

    toggleFilterDropdown(dropdown) {
        // Aktuelles Dropdown öffnen/schließen
        const content = dropdown.querySelector('.filter-dropdown-content');
        const isCurrentlyOpen = content.style.display === 'block';

        if (isCurrentlyOpen) {
            // Dropdown schließen (OHNE Filter anzuwenden)
            content.style.display = 'none';
        } else {
            // Alle anderen Dropdowns schließen (OHNE Filter anzuwenden)
            this.closeAllFilterDropdowns(false);
            // Aktuelles Dropdown öffnen
            content.style.display = 'block';
        }

        // Arrow-Icon drehen
        const arrow = dropdown.querySelector('.filter-arrow');
        if (arrow) {
            arrow.style.transform = isCurrentlyOpen ? 'rotate(0deg)' : 'rotate(180deg)';
        }
    }

    closeAllFilterDropdowns(applyFilters = false) {
        // Alle Dropdowns schließen
        document.querySelectorAll('.filter-dropdown-content').forEach(content => {
            content.style.display = 'none';
        });

        // Alle Pfeile zurücksetzen
        document.querySelectorAll('.filter-arrow').forEach(arrow => {
            arrow.style.transform = 'rotate(0deg)';
        });

        // Filter nur anwenden wenn explizit gewünscht
        if (applyFilters) {
            this.applyFilters();
        }
    }

    updateAllFilterCheckboxes() {
        const filterTypes = ['kategorie', 'sprache', 'kategorie'];
        const prefixes = ['', 'mobile-'];

        prefixes.forEach(prefix => {
            filterTypes.forEach(filterType => {
                const optionsContainer = document.getElementById(`${prefix}${filterType}-filter-options`);
                if (optionsContainer) {
                    // Alle Checkboxen (inklusive "Alle [Kategorie]") setzen
                    const checkboxes = optionsContainer.querySelectorAll('input[type="checkbox"]');
                    checkboxes.forEach(checkbox => {
                        if (checkbox.value === 'alle') {
                            // "Alle [Kategorie]" Checkbox: aktiviert wenn alle anderen aktiviert sind
                            const individualCheckboxes = optionsContainer.querySelectorAll('input[type="checkbox"]:not([value="alle"])');
                            const allIndividualSelected = Array.from(individualCheckboxes).every(cb =>
                                this.selectedFilters[filterType].has(cb.value)
                            );
                            checkbox.checked = allIndividualSelected;
                        } else {
                            // Individuelle Checkboxen
                            checkbox.checked = this.selectedFilters[filterType].has(checkbox.value);
                        }
                    });
                }
            });
        });

        // Button-Texte aktualisieren
        filterTypes.forEach(filterType => {
            this.updateFilterButtonText(filterType);
        });
    }

    syncAllCategoryCheckboxes() {
        const filterTypes = ['kategorie', 'sprache', 'kategorie'];
        const prefixes = ['', 'mobile-'];

        prefixes.forEach(prefix => {
            filterTypes.forEach(filterType => {
                const optionsContainer = document.getElementById(`${prefix}${filterType}-filter-options`);
                if (optionsContainer) {
                    const allCheckbox = optionsContainer.querySelector('input[value="alle"]');
                    const individualCheckboxes = optionsContainer.querySelectorAll('input[type="checkbox"]:not([value="alle"])');

                    if (allCheckbox && individualCheckboxes.length > 0) {
                        // Prüfen ob alle individuellen Checkboxen aktiviert sind
                        const allIndividualChecked = Array.from(individualCheckboxes).every(cb => cb.checked);
                        allCheckbox.checked = allIndividualChecked;
                    }
                }
            });
        });
    }



    setupLegacyFilters() {
        // Fallback für alte Filter-Struktur
        console.log('Setting up legacy filters');

        // Alle Filter als ausgewählt setzen (zeigt alle Geschichten)
        const kategories = [...new Set(this.stories.map(story => story.kategorie))];
        const sprachen = [...new Set(this.stories.map(story => story.sprache))];
        const altersgruppen = [...new Set(this.stories.map(story => story.altersgruppe))];

        this.selectedFilters.kategorie = new Set(kategories);
        this.selectedFilters.sprache = new Set(sprachen);
        this.selectedFilters.altersgruppe = new Set(altersgruppen);

        // Gefilterte Storys initial auf alle Storys setzen
        this.filteredStories = [...this.stories];

        // Filter-Status initial setzen
        this.updateFilterStatus();

        // Sofort rendern
        this.renderMobileStoryList();
    }



    setupFilterEventListeners() {
        // Dropdown-Buttons
        const filterTypes = ['kategorie', 'sprache', 'kategorie'];
        const prefixes = ['', 'mobile-'];

        prefixes.forEach(prefix => {
            filterTypes.forEach(filterType => {
                const btn = document.getElementById(`${prefix}${filterType}-filter-btn`);
                const dropdown = document.getElementById(`${prefix}${filterType}-filter-dropdown`);

                if (btn && dropdown) {
                    btn.addEventListener('click', (e) => {
                        e.stopPropagation();
                        this.toggleFilterDropdown(dropdown);
                    });
                }
            });
        });



        // Dropdown schließen wenn außerhalb geklickt wird
        document.addEventListener('click', () => {
            this.closeAllFilterDropdowns();
        });
    }





    updateFilterButtonText(filterType) {
        const selectedCount = this.selectedFilters[filterType].size;
        const totalOptionsElement = document.querySelector(`#${filterType}-filter-options`);
        const totalOptions = totalOptionsElement ?
            totalOptionsElement.querySelectorAll('input[type="checkbox"]').length : 0;

        let text;
        if (selectedCount === 0) {
            text = `Keine ${this.getFilterDisplayName(filterType)}`;
        } else if (selectedCount === totalOptions && totalOptions > 0) {
            text = `Alle ${this.getFilterDisplayName(filterType)}`;
        } else {
            text = `${selectedCount} ${this.getFilterDisplayName(filterType)}`;
        }

        // Desktop Button aktualisieren
        const desktopBtn = document.getElementById(`${filterType}-filter-btn`);
        if (desktopBtn) {
            const textElement = desktopBtn.querySelector('.filter-text');
            if (textElement) {
                textElement.textContent = text;
            }
        }

        // Mobile Button aktualisieren
        const mobileBtn = document.getElementById(`mobile-${filterType}-filter-btn`);
        if (mobileBtn) {
            const textElement = mobileBtn.querySelector('.filter-text');
            if (textElement) {
                textElement.textContent = text;
            }
        }
    }

    getFilterDisplayName(filterType) {
        const displayNames = {
            kategorie: 'Kategories',
            sprache: 'Sprachen',
            kategorie: 'Alben'
        };
        return displayNames[filterType] || filterType;
    }

    toggleFilterDropdown(dropdown) {
        // Alle anderen Dropdowns schließen
        this.closeAllFilterDropdowns();

        // Aktuelles Dropdown öffnen/schließen
        const content = dropdown.querySelector('.filter-dropdown-content');
        const isOpen = content.style.display === 'block';
        content.style.display = isOpen ? 'none' : 'block';

        // Arrow-Icon drehen
        const arrow = dropdown.querySelector('.filter-arrow');
        arrow.style.transform = isOpen ? 'rotate(0deg)' : 'rotate(180deg)';
    }

    closeAllFilterDropdowns() {
        document.querySelectorAll('.filter-dropdown-content').forEach(content => {
            content.style.display = 'none';
        });
        document.querySelectorAll('.filter-arrow').forEach(arrow => {
            arrow.style.transform = 'rotate(0deg)';
        });
    }

    updateAllFilterCheckboxes() {
        const filterTypes = ['kategorie', 'sprache', 'kategorie'];
        const prefixes = ['', 'mobile-'];

        prefixes.forEach(prefix => {
            filterTypes.forEach(filterType => {
                const optionsContainer = document.getElementById(`${prefix}${filterType}-filter-options`);
                if (optionsContainer) {
                    const checkboxes = optionsContainer.querySelectorAll('input[type="checkbox"]');
                    checkboxes.forEach(checkbox => {
                        checkbox.checked = this.selectedFilters[filterType].has(checkbox.value);
                    });
                }
            });
        });

        // Button-Texte aktualisieren
        filterTypes.forEach(filterType => {
            this.updateFilterButtonText(filterType);
        });
    }
    
    applyFilters() {
        // Sicherheitsüberprüfung
        if (!this.stories || this.stories.length === 0) {
            return;
        }

        // Multiple Choice Filter anwenden - wenn nichts ausgewählt ist, nichts anzeigen
        this.filteredStories = this.stories.filter(story => {
            const kategorieMatch = this.selectedFilters.kategorie.size > 0 && this.selectedFilters.kategorie.has(story.kategorie);
            const spracheMatch = this.selectedFilters.sprache.size > 0 && this.selectedFilters.sprache.has(story.sprache);
            const altersgruppeMatch = this.selectedFilters.altersgruppe.size > 0 && this.selectedFilters.altersgruppe.has(story.altersgruppe);

            return kategorieMatch && spracheMatch && altersgruppeMatch;
        });

        // Shuffle-Queue zurücksetzen wenn Filter geändert werden
        if (this.isShuffleMode) {
            this.resetShuffleQueue();
        }

        // Prüfen ob aktuelles Geschichte noch verfügbar ist
        this.handleCurrentStoryNotInFilter();

        // Filter-Status anzeigen
        this.updateFilterStatus();

        // UI aktualisieren
        this.renderMobileStoryList();
    }
    
    resetFilters() {
        // Alle Filter zurücksetzen
        const kategories = [...new Set(this.stories.map(story => story.kategorie))];
        const sprachen = [...new Set(this.stories.map(story => story.sprache))];
        const altersgruppen = [...new Set(this.stories.map(story => story.altersgruppe))];

        this.selectedFilters.kategorie = new Set(kategories);
        this.selectedFilters.sprache = new Set(sprachen);
        this.selectedFilters.altersgruppe = new Set(altersgruppen);

        // Alle Checkboxen aktivieren
        this.updateAllFilterCheckboxes();

        // Gefilterte Storys zurücksetzen
        this.filteredStories = [...this.stories];

        // Shuffle-Queue zurücksetzen wenn Filter zurückgesetzt werden
        if (this.isShuffleMode) {
            this.resetShuffleQueue();
        }

        // Prüfen ob aktuelles Geschichte noch verfügbar ist (sollte jetzt wieder verfügbar sein)
        this.handleCurrentStoryNotInFilter();

        // Filter-Status zurücksetzen
        this.updateFilterStatus();

        this.renderMobileStoryList();
    }

    toggleMobileFilters() {
        const filterControls = document.getElementById('mobile-filter-controls');
        const toggleBtn = document.getElementById('mobile-filter-toggle');

        if (filterControls.style.display === 'none') {
            filterControls.style.display = 'flex';
            toggleBtn.classList.add('active');
        } else {
            filterControls.style.display = 'none';
            toggleBtn.classList.remove('active');
        }
    }

    toggleMobileMenu(storyId) {
        const dropdown = document.querySelector(`.mobile-menu-dropdown[data-story-id="${storyId}"]`);
        const storyItem = document.querySelector(`.mobile-story-item[data-story-id="${storyId}"]`);

        // Prüfen ob das Menü bereits geöffnet ist
        if (dropdown && dropdown.classList.contains('show')) {
            // Menü schließen wenn es bereits offen ist
            dropdown.classList.remove('show');
            if (storyItem) storyItem.classList.remove('menu-open');
        } else {
            // Alle anderen Menüs schließen
            this.closeMobileMenus();

            // Das gewünschte Menü öffnen
            if (dropdown) {
                dropdown.classList.add('show');
                if (storyItem) storyItem.classList.add('menu-open');
            }
        }
    }

    closeMobileMenus() {
        const allDropdowns = document.querySelectorAll('.mobile-menu-dropdown');
        allDropdowns.forEach(dropdown => {
            dropdown.classList.remove('show');
        });

        // Alle menu-open Klassen entfernen
        const allStoryItems = document.querySelectorAll('.mobile-story-item.menu-open');
        allStoryItems.forEach(item => {
            item.classList.remove('menu-open');
        });
    }

    handleMobileMenuAction(action, storyId) {
        const story = this.stories.find(s => s.id === storyId);
        if (!story) return;

        switch (action) {
            case 'queue':
                this.addToQueue(storyId);
                break;
            case 'hide':
                this.toggleStoryVisibility(storyId);
                break;
            case 'download':
                this.downloadStory(story);
                break;
        }
    }



    addToQueue(storyId) {
        const story = this.stories.find(s => s.id === storyId);
        if (!story) return;

        // Prüfen ob Geschichte bereits in der Queue ist
        if (!this.playQueue.find(queueStory => queueStory.id === storyId)) {
            this.playQueue.push(story);
            this.showQueueNotification(`"${story.titel}" zur Warteschlange hinzugefügt`);
        } else {
            this.showQueueNotification(`"${story.titel}" ist bereits in der Warteschlange`);
        }
    }

    showQueueNotification(message) {
        // Temporäre Benachrichtigung anzeigen
        const notification = document.createElement('div');
        notification.className = 'queue-notification';
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4b879a;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            font-size: 14px;
            max-width: 300px;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        `;

        document.body.appendChild(notification);

        // Animation einblenden
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(0)';
        }, 10);

        // Nach 3 Sekunden ausblenden
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    clearFiltersAndQueue() {
        // Filter zurücksetzen
        this.resetFilters();

        // Warteschlange leeren
        this.playQueue = [];

        // Filter anwenden um UI zu aktualisieren
        this.applyFilters();

        this.showQueueNotification('Filter und Warteschlange zurückgesetzt');
    }




    
    sortTable(column) {
        if (this.currentSort.column === column) {
            this.currentSort.direction = this.currentSort.direction === 'asc' ? 'desc' : 'asc';
        } else {
            this.currentSort.column = column;
            this.currentSort.direction = 'asc';
        }
        
        this.filteredStories.sort((a, b) => {
            let aVal = a[column];
            let bVal = b[column];

            if (column === 'nummer') {
                aVal = parseInt(aVal);
                bVal = parseInt(bVal);
            } else {
                aVal = aVal.toString().toLowerCase();
                bVal = bVal.toString().toLowerCase();
            }

            if (aVal < bVal) return this.currentSort.direction === 'asc' ? -1 : 1;
            if (aVal > bVal) return this.currentSort.direction === 'asc' ? 1 : -1;
            return 0;
        });

        // Shuffle-Queue zurücksetzen wenn sortiert wird
        if (this.isShuffleMode) {
            this.resetShuffleQueue();
        }

        this.updateSortIndicators();
        this.renderMobileStoryList();
    }
    
    updateSortIndicators() {
        document.querySelectorAll('.sort-indicator').forEach(indicator => {
            indicator.className = 'sort-indicator';
        });
        
        const activeHeader = document.querySelector(`[data-column="${this.currentSort.column}"] .sort-indicator`);
        if (activeHeader) {
            activeHeader.className = `sort-indicator ${this.currentSort.direction}`;
        }
    }

    // Hilfsfunktion um zu prüfen ob wir auf Desktop sind
    isDesktop() {
        return window.innerWidth >= 769;
    }

    safeMobileStorySelection(index) {
        // Verhindert schnelles Durchschalten von Storys
        if (this.isChangingStory) {
            return;
        }

        // Debouncing: Vorherige Auswahl abbrechen
        if (this.storySelectionTimeout) {
            clearTimeout(this.storySelectionTimeout);
        }

        this.isChangingStory = true;

        // Story nach kurzer Verzögerung abspielen
        this.storySelectionTimeout = setTimeout(() => {
            this.playStory(index);

            // Player nach erfolgreichem Story-Start öffnen/aktualisieren
            setTimeout(() => {
                if (this.currentStoryIndex === index && !this.audio.error) {
                    if (this.isDesktop()) {
                        this.updateDesktopSidebar();
                    } else {
                        this.openMobilePlayer();
                    }
                }
                this.isChangingStory = false;
            }, 100);
        }, 150);
    }

    updateDesktopSidebar() {
        // Desktop Sidebar mit aktuellem Story aktualisieren
        if (!this.isDesktop()) return;

        // Desktop Event Listeners nur beim ersten Mal einrichten
        if (!this.desktopEventListenersSet) {
            this.setupDesktopEventListeners();
        }

        // Tab-System für Desktop initialisieren
        this.initializeDesktopTabs();

        // Player-Zustand synchronisieren
        if (this.currentStoryIndex !== -1) {
            const story = this.stories[this.currentStoryIndex];
            this.updateDesktopPlayerInfo(story);
            this.syncDesktopProgress();
        }

        // Cover laden
        const storyToShowCover = this.currentStoryIndex !== -1 ?
            this.stories[this.currentStoryIndex] :
            (this.stories.length > 0 ? this.stories[0] : null);

        if (storyToShowCover) {
            this.loadDesktopKategorieCoverBackground(storyToShowCover.titel);
        }
    }

    playStory(index) {
        if (index < 0 || index >= this.stories.length) return;

        this.currentStoryIndex = index;
        const story = this.stories[index];

        // UI aktualisieren
        this.updatePlayerInfo(story);
        this.updatePlayingState();

        // Audio laden und abspielen (falls vorhanden)
        if (story.audioDateiname && story.audioDateiname.trim() !== '') {
            this.audio.src = `audio/${story.audioDateiname}`;
            this.audio.load();

            // Abspielen
            this.audio.play().then(() => {
                // isPlaying wird durch 'play' Event gesetzt
                // Mini-Player anzeigen wenn im mobilen Layout und Modal nicht geöffnet
                if (window.innerWidth <= 768 && this.modalOverlay.style.display !== 'flex') {
                    this.miniPlayer.style.display = 'flex';
                }

                // Skip-Flag zurücksetzen nach erfolgreichem Laden
                this.isSkipping = false;
            }).catch(error => {
                console.error('Fehler beim Abspielen:', error);
                this.handleAudioError(error);
                // Skip-Flag auch bei Fehlern zurücksetzen
                this.isSkipping = false;
            });
        } else {
            // Kein Audio verfügbar - nur UI aktualisieren
            this.showAudioPlaceholder();
            // Mini-Player trotzdem anzeigen für PDF-Download
            if (window.innerWidth <= 768 && this.modalOverlay.style.display !== 'flex') {
                this.miniPlayer.style.display = 'flex';
            }
        }
    }

    showAudioPlaceholder() {
        // Zeigt an, dass kein Audio verfügbar ist
        this.isPlaying = false;
        this.updatePlayingState();

        // Optional: Benachrichtigung anzeigen
        console.log('Kein Audio für diese Geschichte verfügbar');
    }

    // Hilfsfunktion: Spielt ein Geschichte basierend auf der gefilterten Liste
    playStoryFromFiltered(filteredIndex) {
        if (filteredIndex < 0 || filteredIndex >= this.filteredStories.length) return;

        const story = this.filteredStories[filteredIndex];
        const originalIndex = this.stories.findIndex(s => s.id === story.id);

        if (originalIndex !== -1) {
            this.playStory(originalIndex);
        }
    }

    // Hilfsfunktion: Findet den Index eines Storys in der gefilterten Liste
    getCurrentFilteredIndex() {
        if (this.currentStoryIndex === -1) return -1;

        const currentStory = this.stories[this.currentStoryIndex];
        return this.filteredStories.findIndex(story => story.id === currentStory.id);
    }

    // Hilfsfunktion: Prüft ob das aktuelle Geschichte noch in der gefilterten Liste ist
    isCurrentStoryInFilteredList() {
        return this.getCurrentFilteredIndex() !== -1;
    }

    // Hilfsfunktion: Behandelt Situationen, wo das aktuelle Geschichte nicht mehr verfügbar ist
    handleCurrentStoryNotInFilter() {
        if (this.currentStoryIndex !== -1 && this.isPlaying && !this.isCurrentStoryInFilteredList()) {
            // Aktuelles Geschichte ist nicht mehr in der gefilterten Liste
            if (this.filteredStories.length > 0) {
                // Zum ersten verfügbaren Geschichte wechseln
                this.playStoryFromFiltered(0);
            } else {
                // Keine Geschichteer verfügbar - Player stoppen
                this.audio.pause();
                // isPlaying wird durch 'pause' Event gesetzt
            }
        }
    }

    updatePlayerInfo(story) {
        // Mini Player aktualisieren
        this.miniStoryTitle.textContent = story.titel;

        // Kategorie Cover laden
        this.loadKategorieCover(story.titel);

        // Lyrics werden jetzt nur im Modal und Desktop-Sidebar angezeigt

        // Modal auch aktualisieren wenn geöffnet
        this.updateModalPlayerInfo(story);

        // Desktop Sidebar auch aktualisieren wenn auf Desktop
        if (this.isDesktop()) {
            this.updateDesktopPlayerInfo(story);
            this.loadDesktopKategorieCoverBackground(story.titel);
        }

        // Media Session für Sperrbildschirm/Benachrichtigungen aktualisieren
        this.updateMediaSession(story);
    }

    updateModalPlayerInfo(story) {
        // Story-Titel im Titel-Container aktualisieren
        const modalStoryTitle = document.getElementById('modal-story-title');
        if (modalStoryTitle) {
            modalStoryTitle.textContent = story.titel;
        }

        // Artist im Titel-Container aktualisieren
        const modalStoryArtist = document.getElementById('modal-story-artist');
        if (modalStoryArtist) {
            modalStoryArtist.textContent = 'Jana Breitmar';
        }

        // Alte Modal-Elemente (falls noch vorhanden) aktualisieren
        const modalArtist = document.getElementById('modal-artist');
        if (modalArtist) {
            modalArtist.textContent = 'Jana Breitmar';
        }

        // Kategorie aktualisieren
        const modalKategorieName = document.getElementById('modal-kategorie-name');
        if (modalKategorieName) {
            modalKategorieName.textContent = story.kategorie;
        }

        // Bei Geschichtewechsel immer zum Cover-Tab zurückkehren
        this.switchModalTab('cover');

        // Kategorie Cover als Hintergrund des Cover-Tabs setzen - immer laden wenn Modal existiert
        this.loadModalKategorieCoverBackground(story.titel);

        // Info-Tab aktualisieren
        this.updateModalInfoTab(story);

        // Lyrics und Translation Tabs aktualisieren
        this.updateModalLyricsTabs(story);

        // Lyrics aktualisieren
        this.updateModalLyrics(story);
    }

    updateModalLyrics(story) {
        const modalContent = document.querySelector('.modal-content');
        if (!modalContent) return;

        // Lyrics Content aktualisieren
        const modalLyricsContent = modalContent.querySelector('#lyrics-content');
        if (modalLyricsContent) {
            if (story.lyrics && story.lyrics.trim()) {
                let formattedLyrics = this.formatLyrics(story.lyrics);
                modalLyricsContent.innerHTML = `<p>${formattedLyrics}</p>`;
            } else {
                modalLyricsContent.innerHTML = '<p class="no-lyrics">Keine Lyrics verfügbar für dieses Geschichte.</p>';
            }
        }

        // Lyrics Translation aktualisieren
        const modalLyricsTranslation = modalContent.querySelector('#lyrics-translation');
        if (modalLyricsTranslation) {
            if (this.hasTranslation(story)) {
                let formattedTranslation = this.formatLyrics(story.translation);
                modalLyricsTranslation.innerHTML = `<p>${formattedTranslation}</p>`;
            } else {
                modalLyricsTranslation.innerHTML = '<p class="no-lyrics">Keine deutsche Übersetzung verfügbar.</p>';
            }
        }

        // Lyrics Info aktualisieren (jetzt Interpretation)
        const modalLyricsInfo = modalContent.querySelector('#lyrics-info');
        if (modalLyricsInfo) {
            if (this.hasInfo(story)) {
                let formattedInfo = this.formatLyrics(story.interpretation);
                modalLyricsInfo.innerHTML = `<p>${formattedInfo}</p>`;
            } else {
                modalLyricsInfo.innerHTML = '<p class="no-lyrics">Keine zusätzlichen Informationen verfügbar.</p>';
            }
        }

        // Tabs im Modal konfigurieren
        this.configureModalTabs(story);
    }

    configureModalTabs(story) {
        const modalContent = document.querySelector('.modal-content');
        if (!modalContent) return;

        const hasTranslation = this.hasTranslation(story);
        const hasInfo = this.hasInfo(story);

        // Tab-Buttons im Modal anzeigen/verstecken
        const modalTranslationTab = modalContent.querySelector('[data-tab="translation"]');
        const modalInfoTab = modalContent.querySelector('[data-tab="info"]');

        if (modalTranslationTab) {
            if (hasTranslation) {
                modalTranslationTab.style.display = 'block';
            } else {
                modalTranslationTab.style.display = 'none';
            }
        }

        if (modalInfoTab) {
            if (hasInfo) {
                modalInfoTab.style.display = 'block';
            } else {
                modalInfoTab.style.display = 'none';
            }
        }

        // Lyrics Tabs im Modal sichtbar machen
        const modalLyricsTabs = modalContent.querySelector('#lyrics-tabs');
        if (modalLyricsTabs) {
            modalLyricsTabs.style.display = 'flex';
        }

        // Standard: Lyrics Tab aktiv im Modal
        this.switchModalLyricsTab('lyrics');
    }

    switchModalLyricsTab(tabType) {
        const modalContent = document.querySelector('.modal-content');
        if (!modalContent) return;

        // Tab-Buttons im Modal aktualisieren
        modalContent.querySelectorAll('.lyrics-tab').forEach(tab => {
            tab.classList.remove('active');
        });

        const modalTargetTab = modalContent.querySelector(`[data-tab="${tabType}"]`);
        if (modalTargetTab) {
            modalTargetTab.classList.add('active');
        }

        // Alle Content-Container im Modal verstecken
        const modalLyricsContent = modalContent.querySelector('#lyrics-content');
        const modalLyricsTranslation = modalContent.querySelector('#lyrics-translation');
        const modalLyricsInfo = modalContent.querySelector('#lyrics-info');

        if (modalLyricsContent) modalLyricsContent.style.display = 'none';
        if (modalLyricsTranslation) modalLyricsTranslation.style.display = 'none';
        if (modalLyricsInfo) modalLyricsInfo.style.display = 'none';

        // Gewählten Content im Modal anzeigen
        switch(tabType) {
            case 'lyrics':
                if (modalLyricsContent) modalLyricsContent.style.display = 'block';
                break;
            case 'translation':
                if (modalLyricsTranslation) modalLyricsTranslation.style.display = 'block';
                break;
            case 'info':
                if (modalLyricsInfo) modalLyricsInfo.style.display = 'block';
                break;
        }
    }

    // Hilfsfunktion um Dateinamen für Cover-Bilder zu normalisieren
    getCoverFilename(storyTitle) {
        // Spezielle Behandlung für Storys mit problematischen Zeichen
        if (storyTitle === "We're Built for This") {
            return "Were Built for This";
        }

        // Für alle anderen Storys den ursprünglichen Titel verwenden
        return storyTitle;
    }

    // Dynamische Story-Info basierend auf aktiven Filtern
    getDynamicStoryInfo(story) {
        const activeFilters = this.getActiveFilters();
        const infoParts = [];

        // Nur die Eigenschaften anzeigen, die aktiv gefiltert werden
        if (activeFilters.kategorie.length > 0 && activeFilters.kategorie.length < this.getAllUniqueValues('kategorie').length) {
            infoParts.push(story.kategorie);
        }
        if (activeFilters.sprache.length > 0 && activeFilters.sprache.length < this.getAllUniqueValues('sprache').length) {
            infoParts.push(story.sprache);
        }
        if (activeFilters.kategorie.length > 0 && activeFilters.kategorie.length < this.getAllUniqueValues('kategorie').length) {
            infoParts.push(story.kategorie);
        }

        // Fallback: Wenn keine spezifischen Filter aktiv sind, Kategorie anzeigen
        if (infoParts.length === 0) {
            return story.kategorie;
        }

        return infoParts.join(', ');
    }

    // Aktive Filter ermitteln
    getActiveFilters() {
        return {
            kategorie: Array.from(this.selectedFilters.kategorie),
            sprache: Array.from(this.selectedFilters.sprache),
            kategorie: Array.from(this.selectedFilters.kategorie)
        };
    }

    // Alle eindeutigen Werte für einen Filter-Typ
    getAllUniqueValues(filterType) {
        return [...new Set(this.stories.map(story => story[filterType]))];
    }

    // Filter-Status anzeigen
    updateFilterStatus() {
        const filterStatusElement = document.getElementById('filter-status');
        const filterStatusText = document.getElementById('filter-status-text');

        if (!filterStatusElement || !filterStatusText) return;

        const hiddenStorys = this.stories.filter(story => story.isHidden);
        const activeFilters = this.getActiveFilters();
        const allValues = {
            kategorie: this.getAllUniqueValues('kategorie'),
            sprache: this.getAllUniqueValues('sprache'),
            kategorie: this.getAllUniqueValues('kategorie')
        };

        const filterParts = [];



        // Prüfen welche Filter aktiv sind (nicht alle Werte ausgewählt)
        if (activeFilters.kategorie.length > 0 && activeFilters.kategorie.length < allValues.kategorie.length) {
            if (activeFilters.kategorie.length === 1) {
                filterParts.push(`Kategorie: ${activeFilters.kategorie[0]}`);
            } else {
                filterParts.push(`Kategories: ${activeFilters.kategorie.join(', ')}`);
            }
        }

        if (activeFilters.sprache.length > 0 && activeFilters.sprache.length < allValues.sprache.length) {
            if (activeFilters.sprache.length === 1) {
                filterParts.push(`Sprache: ${activeFilters.sprache[0]}`);
            } else {
                filterParts.push(`Sprachen: ${activeFilters.sprache.join(', ')}`);
            }
        }

        if (activeFilters.kategorie.length > 0 && activeFilters.kategorie.length < allValues.kategorie.length) {
            if (activeFilters.kategorie.length === 1) {
                filterParts.push(`Kategorie: ${activeFilters.kategorie[0]}`);
            } else {
                filterParts.push(`Alben: ${activeFilters.kategorie.join(', ')}`);
            }
        }

        // Prüfen ob ausgeblendete Storys existieren und Hinweis anzeigen
        if (hiddenStorys.length > 0 && filterParts.length === 0) {
            filterStatusText.textContent = `${hiddenStorys.length} Geschichte${hiddenStorys.length === 1 ? '' : 'er'} ausgeblendet - Klicke hier um sie wieder anzuzeigen`;
            filterStatusElement.style.display = 'block';
            filterStatusElement.style.cursor = 'pointer';
            filterStatusElement.onclick = () => this.unhideAllStorys();
        } else if (filterParts.length > 0) {
            const visibleCount = this.filteredStories.filter(story => !story.isHidden).length;
            let statusText = `Gefiltert nach: ${filterParts.join(' | ')} (${visibleCount} Geschichteer)`;
            if (hiddenStorys.length > 0) {
                statusText += ` - ${hiddenStorys.length} ausgeblendet`;
            }
            filterStatusText.textContent = statusText;
            filterStatusElement.style.display = 'block';
            filterStatusElement.style.cursor = 'default';
            filterStatusElement.onclick = null;
        } else {
            filterStatusElement.style.display = 'none';
            filterStatusElement.style.cursor = 'default';
            filterStatusElement.onclick = null;
        }
    }

    // Alle ausgeblendeten Storys wieder einblenden
    unhideAllStorys() {
        this.stories.forEach(story => {
            if (story.isHidden) {
                story.isHidden = false;
            }
        });

        // Shuffle-Queue zurücksetzen
        if (this.isShuffleMode) {
            this.resetShuffleQueue();
        }

        // UI aktualisieren
        this.updateFilterStatus();
        this.renderMobileStoryList();

        // Benachrichtigung anzeigen
        this.showQueueNotification('Alle ausgeblendeten Geschichteer wurden wieder angezeigt');
    }

    loadKategorieCover(storyTitle) {
        // Alte Desktop-Player Kategorie-Cover (falls vorhanden)
        const kategorieCover = document.getElementById('kategorie-cover');
        const miniKategorieCover = document.querySelector('.mini-kategorie-cover');

        // Wenn keine Cover-Elemente vorhanden, nichts tun
        if (!kategorieCover && !miniKategorieCover) {
            return;
        }

        // Für Kindergeschichten das quadratische Cover verwenden
        if (storyTitle === "Tennet und die magische Blume") {
            const coverPath = 'bilder/kindergeschichten/Tennet und die magische Blume-quadrat.png';
            const img = new Image();
            img.onload = () => {
                this.showKategorieCover(kategorieCover, miniKategorieCover, coverPath);
            };
            img.onerror = () => {
                // Fallback zu Standard-Verhalten
                this.loadStandardKategorieCover(storyTitle, kategorieCover, miniKategorieCover);
            };
            img.src = coverPath;
            return;
        }

        // Standard-Verhalten für andere Stories
        this.loadStandardKategorieCover(storyTitle, kategorieCover, miniKategorieCover);
    }

    loadStandardKategorieCover(storyTitle, kategorieCover, miniKategorieCover) {
        // Verschiedene Bildformate versuchen
        const possibleFormats = ['png', 'jpg', 'jpeg', 'webp'];
        const coverFilename = this.getCoverFilename(storyTitle);
        let formatIndex = 0;

        const tryLoadImage = () => {
            if (formatIndex >= possibleFormats.length) {
                // Kein Bild gefunden - Platzhalter anzeigen
                this.showPlaceholderCover(kategorieCover, miniKategorieCover);
                return;
            }

            const format = possibleFormats[formatIndex];
            const coverPath = `bilder/kategorie-covers/${coverFilename}.${format}`;

            const img = new Image();
            img.onload = () => {
                // Bild gefunden - anzeigen
                this.showKategorieCover(kategorieCover, miniKategorieCover, coverPath);
            };

            img.onerror = () => {
                // Nächstes Format versuchen
                formatIndex++;
                tryLoadImage();
            };

            img.src = coverPath;
        };

        tryLoadImage();
    }

    showKategorieCover(kategorieCover, miniKategorieCover, coverPath) {
        // Hauptplayer Kategorie-Cover (falls vorhanden)
        if (kategorieCover) {
            kategorieCover.style.backgroundImage = `url('${coverPath}')`;
            kategorieCover.style.backgroundSize = 'cover';
            kategorieCover.style.backgroundPosition = 'center';
            kategorieCover.style.backgroundRepeat = 'no-repeat';
            kategorieCover.innerHTML = '';
            kategorieCover.classList.add('has-cover');
        }

        // Mini-Player Kategorie-Cover
        if (miniKategorieCover) {
            miniKategorieCover.style.backgroundImage = `url('${coverPath}')`;
            miniKategorieCover.style.backgroundSize = 'cover';
            miniKategorieCover.style.backgroundPosition = 'center';
            miniKategorieCover.style.backgroundRepeat = 'no-repeat';
            miniKategorieCover.innerHTML = '';
            miniKategorieCover.classList.add('has-cover');
        }

        // Media Session mit korrektem Cover aktualisieren
        this.currentCoverPath = coverPath;
        if (this.currentStoryIndex !== -1) {
            this.updateMediaSessionWithCover(this.stories[this.currentStoryIndex], coverPath);
        }
    }

    showPlaceholderCover(kategorieCover, miniKategorieCover) {
        // Hauptplayer Platzhalter
        kategorieCover.style.backgroundImage = '';
        kategorieCover.classList.remove('has-cover');
        kategorieCover.innerHTML = `
            <svg class="placeholder-icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
            </svg>
        `;

        // Mini-Player Platzhalter
        if (miniKategorieCover) {
            miniKategorieCover.style.backgroundImage = '';
            miniKategorieCover.classList.remove('has-cover');
            miniKategorieCover.innerHTML = `
                <svg viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
                </svg>
            `;
        }

        // Media Session ohne Cover aktualisieren (Fallback auf Favicon)
        this.currentCoverPath = null;
    }

    updateMediaSession(story) {
        // Media Session API für Sperrbildschirm/Benachrichtigungen
        if ('mediaSession' in navigator) {
            // Action Handlers nur einmal setzen
            this.setupMediaSessionHandlers();

            // Story-Metadaten setzen
            const artwork = this.currentCoverPath ?
                this.createArtworkArray(this.currentCoverPath) :
                this.getMediaSessionArtwork(story.titel);

            navigator.mediaSession.metadata = new MediaMetadata({
                title: story.titel,
                artist: 'Jana Breitmar',
                kategorie: story.kategorie,
                artwork: artwork
            });
        }
    }

    setupMediaSessionHandlers() {
        // Action Handlers nur einmal setzen (vermeidet mehrfache Registrierung)
        if (this.mediaSessionHandlersSet) return;

        navigator.mediaSession.setActionHandler('play', () => {
            this.audio.play();
            // isPlaying wird durch 'play' Event gesetzt
        });

        navigator.mediaSession.setActionHandler('pause', () => {
            this.audio.pause();
            // isPlaying wird durch 'pause' Event gesetzt
        });

        navigator.mediaSession.setActionHandler('previoustrack', () => {
            this.previousStory();
        });

        navigator.mediaSession.setActionHandler('nexttrack', () => {
            this.nextStory();
        });

        // Seek-Funktionalität (optional)
        navigator.mediaSession.setActionHandler('seekto', (details) => {
            if (details.seekTime && this.audio.duration) {
                this.audio.currentTime = details.seekTime;
            }
        });

        this.mediaSessionHandlersSet = true;
    }

    createArtworkArray(coverPath) {
        // Artwork Array für spezifischen Cover-Pfad erstellen
        return [
            { src: coverPath, sizes: '96x96', type: 'image/png' },
            { src: coverPath, sizes: '128x128', type: 'image/png' },
            { src: coverPath, sizes: '192x192', type: 'image/png' },
            { src: coverPath, sizes: '256x256', type: 'image/png' },
            { src: coverPath, sizes: '384x384', type: 'image/png' },
            { src: coverPath, sizes: '512x512', type: 'image/png' }
        ];
    }

    updateMediaSessionPosition() {
        // Media Session Position für Sperrbildschirm aktualisieren
        if ('mediaSession' in navigator && 'setPositionState' in navigator.mediaSession) {
            if (this.audio.duration && !isNaN(this.audio.duration)) {
                navigator.mediaSession.setPositionState({
                    duration: this.audio.duration,
                    playbackRate: this.audio.playbackRate,
                    position: this.audio.currentTime
                });
            }
        }
    }

    updateMediaSessionPlaybackState() {
        // Media Session Playback State aktualisieren
        if ('mediaSession' in navigator) {
            navigator.mediaSession.playbackState = this.isPlaying ? 'playing' : 'paused';
        }
    }

    getMediaSessionArtwork(storyTitle) {
        // Kategorie-Cover URLs für Media Session generieren
        const possibleFormats = ['png', 'jpg', 'jpeg', 'webp'];
        const artworkArray = [];

        possibleFormats.forEach(format => {
            const coverFilename = this.getCoverFilename(storyTitle);
            const coverPath = `bilder/kategorie-covers/${coverFilename}.${format}`;
            // Verschiedene Größen für bessere Kompatibilität
            artworkArray.push(
                { src: coverPath, sizes: '96x96', type: `image/${format}` },
                { src: coverPath, sizes: '128x128', type: `image/${format}` },
                { src: coverPath, sizes: '192x192', type: `image/${format}` },
                { src: coverPath, sizes: '256x256', type: `image/${format}` },
                { src: coverPath, sizes: '384x384', type: `image/${format}` },
                { src: coverPath, sizes: '512x512', type: `image/${format}` }
            );
        });

        return artworkArray;
    }

    updateMediaSessionWithCover(story, coverPath) {
        // Media Session mit spezifischem Cover-Pfad aktualisieren
        if ('mediaSession' in navigator) {
            navigator.mediaSession.metadata = new MediaMetadata({
                title: story.titel,
                artist: 'Jana Breitmar',
                kategorie: story.kategorie,
                artwork: [
                    { src: coverPath, sizes: '96x96', type: 'image/png' },
                    { src: coverPath, sizes: '128x128', type: 'image/png' },
                    { src: coverPath, sizes: '192x192', type: 'image/png' },
                    { src: coverPath, sizes: '256x256', type: 'image/png' },
                    { src: coverPath, sizes: '384x384', type: 'image/png' },
                    { src: coverPath, sizes: '512x512', type: 'image/png' }
                ]
            });
        }
    }

    loadMobileKategorieCover(mobileKategorieCover, storyTitle) {
        const possibleFormats = ['png', 'jpg', 'jpeg', 'webp'];
        const coverFilename = this.getCoverFilename(storyTitle);
        let formatIndex = 0;

        const tryLoadImage = () => {
            if (formatIndex >= possibleFormats.length) {
                // Kein Bild gefunden - Platzhalter anzeigen
                mobileKategorieCover.style.backgroundImage = '';
                mobileKategorieCover.classList.remove('has-cover');
                mobileKategorieCover.innerHTML = `
                    <svg class="placeholder-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
                    </svg>
                `;
                return;
            }

            const format = possibleFormats[formatIndex];
            const coverPath = `bilder/kategorie-covers/${coverFilename}.${format}`;

            const img = new Image();
            img.onload = () => {
                // Bild gefunden - als IMG-Element anzeigen (wie im Modal)
                mobileKategorieCover.style.backgroundImage = '';
                mobileKategorieCover.innerHTML = `<img src="${coverPath}" alt="Kategorie Cover">`;
                mobileKategorieCover.classList.add('has-cover');
            };

            img.onerror = () => {
                // Nächstes Format versuchen
                formatIndex++;
                tryLoadImage();
            };

            img.src = coverPath;
        };

        tryLoadImage();
    }

    loadModalKategorieCover(modalKategorieCover, storyTitle) {
        const possibleFormats = ['png', 'jpg', 'jpeg', 'webp'];
        let formatIndex = 0;

        const tryLoadImage = () => {
            if (formatIndex >= possibleFormats.length) {
                // Kein Bild gefunden - Platzhalter anzeigen
                modalKategorieCover.style.backgroundImage = '';
                modalKategorieCover.classList.remove('has-cover');
                modalKategorieCover.innerHTML = `
                    <svg class="placeholder-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
                    </svg>
                `;
                return;
            }

            const format = possibleFormats[formatIndex];
            const coverFilename = this.getCoverFilename(storyTitle);
            const coverPath = `bilder/kategorie-covers/${coverFilename}.${format}`;

            const img = new Image();
            img.onload = () => {
                // Bild gefunden - anzeigen
                modalKategorieCover.style.backgroundImage = `url('${coverPath}')`;
                modalKategorieCover.style.backgroundSize = 'cover';
                modalKategorieCover.style.backgroundPosition = 'center';
                modalKategorieCover.style.backgroundRepeat = 'no-repeat';
                modalKategorieCover.innerHTML = '';
                modalKategorieCover.classList.add('has-cover');
            };

            img.onerror = () => {
                // Nächstes Format versuchen
                formatIndex++;
                tryLoadImage();
            };

            img.src = coverPath;
        };

        tryLoadImage();
    }

    loadModalKategorieCoverBackground(storyTitle) {
        // Funktion mit Retry-Mechanismus für robustere Ausführung
        const attemptLoad = (retryCount = 0) => {
            const coverTab = document.querySelector('.modal-tab-content[data-content="cover"]');

            if (!coverTab) {
                // Wenn Element nicht gefunden und noch Retries übrig, erneut versuchen
                if (retryCount < 3) {
                    setTimeout(() => attemptLoad(retryCount + 1), 100);
                }
                return;
            }

            // Für Kindergeschichten das quadratische Cover verwenden
            if (storyTitle === "Tennet und die magische Blume") {
                const coverPath = 'bilder/kindergeschichten/Tennet und die magische Blume-quadrat.png';
                const img = new Image();
                img.onload = () => {
                    coverTab.style.backgroundImage = `url('${coverPath}')`;
                    coverTab.style.backgroundSize = 'cover';
                    coverTab.style.backgroundPosition = 'center';
                    coverTab.style.backgroundRepeat = 'no-repeat';
                    coverTab.innerHTML = '';
                    coverTab.classList.add('has-cover');
                };
                img.onerror = () => {
                    // Fallback zu Standard-Verhalten
                    this.loadStandardModalCover(coverTab, storyTitle);
                };
                img.src = coverPath;
                return;
            }

            // Standard-Verhalten für andere Stories
            this.loadStandardModalCover(coverTab, storyTitle);
        };

        attemptLoad();
    }

    loadStandardModalCover(coverTab, storyTitle) {
        const possibleFormats = ['png', 'jpg', 'jpeg', 'webp'];
        let formatIndex = 0;

        const tryLoadImage = () => {
            if (formatIndex >= possibleFormats.length) {
                // Kein Bild gefunden - Platzhalter anzeigen
                coverTab.style.backgroundImage = '';
                coverTab.classList.remove('has-cover');
                coverTab.innerHTML = `
                    <svg class="placeholder-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
                    </svg>
                `;
                return;
            }

            const format = possibleFormats[formatIndex];
            const coverFilename = this.getCoverFilename(storyTitle);
            const coverPath = `bilder/kategorie-covers/${coverFilename}.${format}`;

            const img = new Image();
            img.onload = () => {
                // Bild gefunden - anzeigen
                coverTab.style.backgroundImage = `url('${coverPath}')`;
                coverTab.style.backgroundSize = 'cover';
                coverTab.style.backgroundPosition = 'center';
                coverTab.style.backgroundRepeat = 'no-repeat';
                coverTab.innerHTML = '';
                coverTab.classList.add('has-cover');
            };

            img.onerror = () => {
                // Nächstes Format versuchen
                formatIndex++;
                tryLoadImage();
            };

            img.src = coverPath;
        };

        tryLoadImage();
    }

    updatePlayingState() {
        // Alle Zeilen zurücksetzen
        document.querySelectorAll('.story-table tbody tr').forEach(row => {
            row.classList.remove('playing');
        });
        document.querySelectorAll('.mobile-story-item').forEach(item => {
            item.classList.remove('playing');
        });

        // Aktuelle Zeile markieren
        const currentRow = document.querySelector(`[data-story-id="${this.stories[this.currentStoryIndex].id}"]`);
        if (currentRow) {
            currentRow.classList.add('playing');
        }
    }

    togglePlayPause() {
        if (this.currentStoryIndex === -1) {
            this.playStory(0);
            return;
        }

        if (this.isPlaying) {
            this.audio.pause();
            // isPlaying wird durch 'pause' Event gesetzt
        } else {
            this.audio.play().catch(error => {
                console.error('Fehler beim Abspielen:', error);
            });
            // isPlaying wird durch 'play' Event gesetzt
        }
    }

    // Spezielle Funktion für Mobile Play Button - startet mit "In My Arms" beim ersten Mal
    togglePlayPauseWithDefault() {
        if (this.currentStoryIndex === -1) {
            // Finde "In My Arms" in der Hauptliste
            const inMyArmsIndex = this.stories.findIndex(story => story.titel === "In My Arms");
            if (inMyArmsIndex !== -1) {
                this.playStory(inMyArmsIndex);
                return;
            }
            // Fallback: Erstes Geschichte
            this.playStory(0);
            return;
        }

        // Wenn bereits ein Story läuft, normale Play/Pause Funktionalität
        if (this.isPlaying) {
            this.audio.pause();
            // isPlaying wird durch 'pause' Event gesetzt
        } else {
            this.audio.play().catch(error => {
                console.error('Fehler beim Abspielen:', error);
            });
            // isPlaying wird durch 'play' Event gesetzt
        }
    }

    updatePlayButton() {
        const playIcons = document.querySelectorAll('.play-icon');
        const pauseIcons = document.querySelectorAll('.pause-icon');

        if (this.isPlaying) {
            playIcons.forEach(icon => icon.style.display = 'none');
            pauseIcons.forEach(icon => icon.style.display = 'block');
        } else {
            playIcons.forEach(icon => icon.style.display = 'block');
            pauseIcons.forEach(icon => icon.style.display = 'none');
        }

        // Mobile Play Button Titel aktualisieren
        const mobilePlayBtn = document.getElementById('mobile-play-filtered-btn');
        if (mobilePlayBtn) {
            mobilePlayBtn.title = this.isPlaying ? 'Pause' : 'Wiedergabe';
        }

        // Modal Button Zustand auch aktualisieren
        this.updateModalButtonStates();
    }

    previousStory() {
        // Radikales Anti-Spam-System
        const now = Date.now();
        if (now - this.lastSkipTime < this.skipCooldown) {
            console.log(`previousStory: Cooldown active, ignoring call. Time since last: ${now - this.lastSkipTime}ms`);
            return;
        }

        if (this.isSkipping) {
            console.log('previousStory: Already skipping, ignoring call');
            return;
        }

        console.log('previousStory: Starting skip operation');
        this.lastSkipTime = now;
        this.isSkipping = true;

        // Timeout für Debouncing
        if (this.skipDebounceTimeout) {
            clearTimeout(this.skipDebounceTimeout);
        }
        this.skipDebounceTimeout = setTimeout(() => {
            console.log('previousStory: Debounce timeout completed, resetting skip flag');
            this.isSkipping = false;
        }, 300); // 300ms Debounce-Zeit - schnell genug für flüssiges Skippen

        // Prüfen ob gefilterte Liste leer ist
        if (this.filteredStories.length === 0) {
            this.isSkipping = false;
            return;
        }

        // Standard-Verhalten: Neustart vs. vorheriges Geschichte
        const currentTime = this.audio.currentTime || 0;

        // Wenn Story länger als 3 Sekunden läuft, neustartet
        if (currentTime > 3) {
            console.log(`previousStory: Restarting current story (currentTime: ${currentTime}s)`);
            this.audio.currentTime = 0;
            this.isSkipping = false;
            return;
        }

        // Wenn Story weniger als 3 Sekunden läuft, zum vorherigen Story
        console.log(`previousStory: Going to previous story (currentTime: ${currentTime}s)`);
        this.goToPreviousStory();
    }

    goToPreviousStory() {
        if (this.isShuffleMode) {
            const previousIndex = this.getPreviousShuffleStory();
            this.playStory(previousIndex);
        } else {
            const currentFilteredIndex = this.getCurrentFilteredIndex();
            if (currentFilteredIndex <= 0) {
                // Zum letzten Geschichte in der gefilterten Liste
                this.playStoryFromFiltered(this.filteredStories.length - 1);
            } else {
                // Zum vorherigen Geschichte in der gefilterten Liste
                this.playStoryFromFiltered(currentFilteredIndex - 1);
            }
        }
    }

    nextStory() {
        // Radikales Anti-Spam-System
        const now = Date.now();
        if (now - this.lastSkipTime < this.skipCooldown) {
            console.log(`nextStory: Cooldown active, ignoring call. Time since last: ${now - this.lastSkipTime}ms`);
            return;
        }

        if (this.isSkipping) {
            console.log('nextStory: Already skipping, ignoring call');
            return;
        }

        console.log('nextStory: Starting skip operation');
        this.lastSkipTime = now;
        this.isSkipping = true;

        // Timeout für Debouncing
        if (this.skipDebounceTimeout) {
            clearTimeout(this.skipDebounceTimeout);
        }
        this.skipDebounceTimeout = setTimeout(() => {
            console.log('nextStory: Debounce timeout completed, resetting skip flag');
            this.isSkipping = false;
        }, 300); // 300ms Debounce-Zeit - schnell genug für flüssiges Skippen

        // Prüfen ob gefilterte Liste leer ist
        if (this.filteredStories.length === 0) {
            this.isSkipping = false;
            return;
        }

        // Single Repeat: Aktuelles Geschichte wiederholen
        if (this.repeatMode === 'single' && this.currentStoryIndex !== -1) {
            this.playStory(this.currentStoryIndex);
            return;
        }

        // Warteschlange prüfen (hat Priorität vor Shuffle/Normal)
        if (this.playQueue.length > 0) {
            const nextStory = this.playQueue.shift(); // Erstes Geschichte aus Queue entfernen
            const storyIndex = this.stories.findIndex(story => story.id === nextStory.id);
            if (storyIndex !== -1) {
                this.playStory(storyIndex);
                return;
            }
        }

        if (this.isShuffleMode) {
            const nextIndex = this.getNextShuffleStory();
            if (nextIndex !== -1) {
                this.playStory(nextIndex);
            }
        } else {
            const currentFilteredIndex = this.getCurrentFilteredIndex();
            if (currentFilteredIndex >= this.filteredStories.length - 1) {
                // Am Ende der Playlist
                if (this.repeatMode === 'playlist') {
                    // Playlist wiederholen: Zum ersten Geschichte
                    this.playStoryFromFiltered(0);
                } else {
                    // Kein Repeat: Stoppen
                    this.audio.pause();
                    // isPlaying wird durch 'pause' Event gesetzt
                }
            } else {
                // Zum nächsten Geschichte in der gefilterten Liste
                this.playStoryFromFiltered(currentFilteredIndex + 1);
            }
        }
    }

    toggleShuffle() {
        this.isShuffleMode = !this.isShuffleMode;
        const shuffleBtn = document.getElementById('shuffle-btn');
        const mobileShuffleBtn = document.getElementById('mobile-shuffle-btn');
        const desktopShuffleBtn = document.getElementById('desktop-shuffle-btn');

        if (shuffleBtn) shuffleBtn.classList.toggle('active', this.isShuffleMode);
        if (mobileShuffleBtn) mobileShuffleBtn.classList.toggle('active', this.isShuffleMode);
        if (desktopShuffleBtn) desktopShuffleBtn.classList.toggle('active', this.isShuffleMode);

        // Modal Button Zustand auch aktualisieren
        this.updateModalButtonStates();

        // Shuffle-Queue zurücksetzen wenn Shuffle aktiviert wird
        if (this.isShuffleMode) {
            this.resetShuffleQueue();
        }
    }

    // Shuffle-Queue verwalten
    resetShuffleQueue() {
        // Alle verfügbaren (nicht versteckten) Geschichteer aus der gefilterten Liste
        const availableStorys = this.filteredStories.filter(story => !story.isHidden);
        this.shuffleQueue = [...availableStorys];
        this.shuffleHistory = [];

        // Aktuelles Geschichte aus der Queue entfernen, falls es gespielt wird
        if (this.currentStoryIndex !== -1) {
            const currentStory = this.stories[this.currentStoryIndex];
            const currentInQueue = this.shuffleQueue.findIndex(story => story.id === currentStory.id);
            if (currentInQueue !== -1) {
                this.shuffleQueue.splice(currentInQueue, 1);
                this.shuffleHistory.push(currentStory);
            }
        }
    }

    getNextShuffleStory() {
        // Wenn Queue leer ist, alle Geschichteer wieder hinzufügen (außer dem aktuellen)
        if (this.shuffleQueue.length === 0) {
            this.resetShuffleQueue();
        }

        // Wenn immer noch leer (nur ein Geschichte verfügbar oder keine verfügbaren Geschichteer), das aktuelle Geschichte wiederholen
        if (this.shuffleQueue.length === 0) {
            if (this.currentStoryIndex !== -1 && this.isCurrentStoryInFilteredList()) {
                return this.currentStoryIndex;
            } else {
                // Kein verfügbares Geschichte
                return -1;
            }
        }

        // Zufälliges Geschichte aus der Queue wählen
        const randomIndex = Math.floor(Math.random() * this.shuffleQueue.length);
        const selectedStory = this.shuffleQueue[randomIndex];

        // Geschichte aus Queue entfernen und zur History hinzufügen
        this.shuffleQueue.splice(randomIndex, 1);
        this.shuffleHistory.push(selectedStory);

        // Original-Index des gewählten Geschichtees finden
        return this.stories.findIndex(story => story.id === selectedStory.id);
    }

    getPreviousShuffleStory() {
        if (this.shuffleHistory.length <= 1) {
            return this.currentStoryIndex; // Kein vorheriges Geschichte verfügbar
        }

        // Aktuelles Geschichte zurück in die Queue
        const currentStory = this.shuffleHistory.pop();
        if (currentStory) {
            this.shuffleQueue.unshift(currentStory);
        }

        // Vorheriges Geschichte aus der History
        const previousStory = this.shuffleHistory[this.shuffleHistory.length - 1];
        return this.stories.findIndex(story => story.id === previousStory.id);
    }

    toggleRepeat() {
        // Durch die drei Modi zyklieren: none -> playlist -> single -> none
        switch (this.repeatMode) {
            case 'none':
                this.repeatMode = 'playlist';
                break;
            case 'playlist':
                this.repeatMode = 'single';
                break;
            case 'single':
                this.repeatMode = 'none';
                break;
        }

        this.updateRepeatButtons();
    }

    updateRepeatButtons() {
        const repeatBtn = document.getElementById('repeat-btn');
        const mobileRepeatBtn = document.getElementById('mobile-repeat-btn');
        const desktopRepeatBtn = document.getElementById('desktop-repeat-btn');

        // Alle Klassen entfernen
        if (repeatBtn) {
            repeatBtn.classList.remove('active', 'single-repeat');
        }
        if (mobileRepeatBtn) {
            mobileRepeatBtn.classList.remove('active', 'single-repeat');
        }
        if (desktopRepeatBtn) {
            desktopRepeatBtn.classList.remove('active', 'single-repeat');
        }

        // Entsprechende Klassen und Inhalte setzen
        switch (this.repeatMode) {
            case 'none':
                // Keine besonderen Klassen oder Inhalte
                this.setRepeatButtonContent(repeatBtn, false);
                this.setRepeatButtonContent(mobileRepeatBtn, false);
                this.setRepeatButtonContent(desktopRepeatBtn, false);
                break;
            case 'playlist':
                if (repeatBtn) repeatBtn.classList.add('active');
                if (mobileRepeatBtn) mobileRepeatBtn.classList.add('active');
                if (desktopRepeatBtn) desktopRepeatBtn.classList.add('active');
                this.setRepeatButtonContent(repeatBtn, false);
                this.setRepeatButtonContent(mobileRepeatBtn, false);
                this.setRepeatButtonContent(desktopRepeatBtn, false);
                break;
            case 'single':
                if (repeatBtn) repeatBtn.classList.add('active', 'single-repeat');
                if (mobileRepeatBtn) mobileRepeatBtn.classList.add('active', 'single-repeat');
                if (desktopRepeatBtn) desktopRepeatBtn.classList.add('active', 'single-repeat');
                this.setRepeatButtonContent(repeatBtn, true);
                this.setRepeatButtonContent(mobileRepeatBtn, true);
                this.setRepeatButtonContent(desktopRepeatBtn, true);
                break;
        }

        // Modal Button Zustand auch aktualisieren
        this.updateModalButtonStates();
    }

    setRepeatButtonContent(button, isSingle) {
        if (!button) return;

        const svg = button.querySelector('svg');
        if (!svg) return;

        if (isSingle) {
            // "1" Symbol hinzufügen
            if (!svg.querySelector('.repeat-one')) {
                const oneText = document.createElementNS('http://www.w3.org/2000/svg', 'text');
                oneText.setAttribute('x', '12');
                oneText.setAttribute('y', '16');
                oneText.setAttribute('text-anchor', 'middle');
                oneText.setAttribute('font-size', '8');
                oneText.setAttribute('font-weight', 'bold');
                oneText.setAttribute('fill', 'currentColor');
                oneText.setAttribute('class', 'repeat-one');
                oneText.textContent = '1';
                svg.appendChild(oneText);
            }
        } else {
            // "1" Symbol entfernen
            const oneText = svg.querySelector('.repeat-one');
            if (oneText) {
                oneText.remove();
            }
        }
    }

    updateProgress() {
        if (this.audio.duration) {
            const progress = (this.audio.currentTime / this.audio.duration) * 100;

            // Desktop Progress Bar (falls vorhanden)
            const progressFill = document.getElementById('progress-fill');
            if (progressFill) {
                progressFill.style.width = `${progress}%`;
            }

            // Desktop Time Display (falls vorhanden)
            const currentTimeDisplay = document.getElementById('current-time');
            if (currentTimeDisplay) {
                currentTimeDisplay.textContent = this.formatTime(this.audio.currentTime);
            }

            // Modal auch aktualisieren wenn geöffnet
            this.updateModalProgressIfOpen();

            // Desktop Sidebar auch aktualisieren wenn auf Desktop
            if (this.isDesktop()) {
                this.syncDesktopProgress();
            }
        }
    }

    updateDuration() {
        // Desktop Duration Display (falls vorhanden)
        const totalTimeDisplay = document.getElementById('total-time');
        if (totalTimeDisplay) {
            totalTimeDisplay.textContent = this.formatTime(this.audio.duration);
        }
        this.updateModalDurationIfOpen();
    }

    updateModalProgressIfOpen() {
        // Nur Modal aktualisieren wenn es geöffnet ist
        if (this.modalOverlay.style.display !== 'flex') return;

        if (!this.audio.duration) return;

        const progress = (this.audio.currentTime / this.audio.duration) * 100;
        const currentTimeText = this.formatTime(this.audio.currentTime);

        const modalProgressFill = document.getElementById('modal-progress-fill');
        const modalCurrentTime = document.getElementById('modal-current-time');

        if (modalProgressFill) {
            modalProgressFill.style.width = `${progress}%`;
        }
        if (modalCurrentTime) {
            modalCurrentTime.textContent = currentTimeText;
        }
    }

    updateModalDurationIfOpen() {
        // Nur Modal aktualisieren wenn es geöffnet ist
        if (this.modalOverlay.style.display !== 'flex') return;

        const modalTotalTime = document.getElementById('modal-total-time');
        if (modalTotalTime) {
            modalTotalTime.textContent = this.formatTime(this.audio.duration);
        }
    }

    formatTime(seconds) {
        if (isNaN(seconds)) return '0:00';
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }

    seekTo(event) {
        const progressContainer = event.currentTarget;
        const rect = progressContainer.getBoundingClientRect();
        const clickX = event.clientX - rect.left;
        const percentage = clickX / rect.width;

        if (this.audio.duration) {
            this.audio.currentTime = percentage * this.audio.duration;
        }
    }

    toggleStoryVisibility(storyId) {
        const story = this.stories.find(s => s.id === storyId);
        if (story) {
            story.isHidden = !story.isHidden;

            // Shuffle-Queue zurücksetzen wenn Sichtbarkeit geändert wird
            if (this.isShuffleMode) {
                this.resetShuffleQueue();
            }

            // Prüfen ob aktuelles Geschichte noch verfügbar ist
            this.handleCurrentStoryNotInFilter();

            // Filter-Status aktualisieren
            this.updateFilterStatus();

            this.renderMobileStoryList();

            // Benachrichtigung anzeigen
            const action = story.isHidden ? 'ausgeblendet' : 'wieder angezeigt';
            this.showQueueNotification(`"${story.titel}" wurde ${action}`);
        }
    }

    toggleAllVisibility() {
        const allHidden = this.stories.every(story => story.isHidden);
        this.stories.forEach(story => {
            story.isHidden = !allHidden;
        });

        // Shuffle-Queue zurücksetzen wenn alle Geschichteer ein-/ausgeblendet werden
        if (this.isShuffleMode) {
            this.resetShuffleQueue();
        }

        // Prüfen ob aktuelles Geschichte noch verfügbar ist
        this.handleCurrentStoryNotInFilter();

        this.renderMobileStoryList();
    }

    downloadStory(storyId) {
        const story = this.stories.find(s => s.id === storyId);
        if (story && story.pdfDateiname) {
            const link = document.createElement('a');
            link.href = `PDFs/kindergeschichten/${story.pdfDateiname}`;
            link.download = `${story.titel} - Jana Breitmar.pdf`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        } else {
            console.warn('Keine PDF-Datei für diese Geschichte verfügbar');
        }
    }

    downloadCurrent() {
        if (this.currentStoryIndex !== -1) {
            this.downloadStory(this.stories[this.currentStoryIndex].id);
        }
    }

    downloadAllFiltered() {
        const filteredStories = this.filteredStories.filter(story => !story.isHidden);

        if (filteredStories.length === 0) {
            alert('Keine Geschichteer zum Herunterladen verfügbar.');
            return;
        }

        // Bestätigung für viele Downloads
        if (filteredStories.length > 10) {
            if (!confirm(`Möchten Sie wirklich ${filteredStories.length} Geschichteer herunterladen?`)) {
                return;
            }
        }

        // Downloads mit Verzögerung starten
        filteredStories.forEach((story, index) => {
            setTimeout(() => {
                this.downloadStory(story.id);
            }, index * 500); // 500ms Verzögerung zwischen Downloads
        });
    }

    // Mobile-spezifische Funktionen
    playFilteredList() {
        const filteredStories = this.filteredStories.filter(story => !story.isHidden);

        if (filteredStories.length === 0) {
            alert('Keine Geschichteer in der gefilterten Liste verfügbar.');
            return;
        }

        // Erstes Geschichte der gefilterten Liste abspielen
        const firstStory = filteredStories[0];
        const storyIndex = this.stories.findIndex(story => story.id === firstStory.id);

        if (storyIndex !== -1) {
            this.playStory(storyIndex);
        }
    }

    downloadFilteredList() {
        this.downloadAllFiltered(); // Verwendet die bereits vorhandene Funktion
    }

    // URL-Parameter prüfen und entsprechend handeln
    checkUrlParameters() {
        const urlParams = new URLSearchParams(window.location.search);
        const playParam = urlParams.get('play');

        if (playParam) {
            // Story finden - sowohl nach Titel als auch nach ID suchen
            let storyIndex = -1;

            // Zuerst nach exakter Titel-Übereinstimmung suchen
            storyIndex = this.stories.findIndex(story =>
                story.titel.toLowerCase() === playParam.toLowerCase()
            );

            // Falls nicht gefunden, nach Story-ID suchen (für bessere URL-Verknüpfung)
            if (storyIndex === -1) {
                const storyId = parseInt(playParam);
                if (!isNaN(storyId)) {
                    storyIndex = this.stories.findIndex(story => story.id === storyId);
                }
            }

            if (storyIndex !== -1) {
                console.log(`Auto-playing story: ${playParam}`);

                // Priorisiertes Laden: Audio sofort laden
                const story = this.stories[storyIndex];
                this.audio.src = `audio/${story.dateiname}`;
                this.audio.load();

                // Story-Index setzen und UI aktualisieren
                this.currentStoryIndex = storyIndex;
                this.updatePlayerInfo(story);

                // Sofortiges Abspielen nach kurzer Verzögerung
                setTimeout(() => {
                    this.audio.play().then(() => {
                        this.updatePlayingState();
                        console.log(`Successfully auto-played: ${story.titel}`);

                        // Auf mobilen Geräten: Modal nach erfolgreichem Start öffnen
                        if (!this.isDesktop()) {
                            setTimeout(() => {
                                this.openMobilePlayer();
                            }, 100);
                        }
                    }).catch(error => {
                        console.error('Fehler beim Auto-Abspielen:', error);
                        // Fallback: Benutzer muss manuell Play drücken
                        this.updatePlayingState();

                        // Modal trotzdem öffnen auf mobilen Geräten
                        if (!this.isDesktop()) {
                            setTimeout(() => {
                                this.openMobilePlayer();
                            }, 100);
                        }
                    });
                }, 300);
            } else {
                console.warn(`Story nicht gefunden: ${playParam}`);
            }

            // URL-Parameter entfernen für saubere URL
            window.history.replaceState({}, document.title, window.location.pathname);
        }
    }

    // Filter nach Info-Werten (Kategorie, Kategorie, Sprache)
    filterByInfoValue(filterType, filterValue) {
        console.log(`filterByInfoValue called: ${filterType} = ${filterValue}`); // Debug

        // Modal schließen
        this.closeMobilePlayer();

        // Kurze Verzögerung für bessere UX
        setTimeout(() => {
            // Filter zurücksetzen
            this.resetFilters();

            // Kurze Verzögerung nach Reset
            setTimeout(() => {
                // Entsprechenden Filter setzen
                this.setFilterValue(filterType, filterValue);

                // Filter anwenden
                this.applyFilters();

                // Zur Storyliste scrollen (mobile Ansicht)
                if (window.innerWidth <= 768) {
                    const storyList = document.getElementById('mobile-story-list');
                    if (storyList) {
                        storyList.scrollIntoView({ behavior: 'smooth' });
                    }
                }
            }, 100);
        }, 200);
    }

    // Hilfsfunktion zum Setzen eines spezifischen Filter-Werts
    setFilterValue(filterType, value) {
        console.log(`Setting filter: ${filterType} = ${value}`); // Debug

        // Filter-Set leeren und nur den gewünschten Wert hinzufügen
        this.selectedFilters[filterType].clear();
        this.selectedFilters[filterType].add(value);

        // Alle Checkboxen für diesen Filter-Typ deaktivieren
        document.querySelectorAll(`#${filterType}-filter-options input[type="checkbox"]`).forEach(cb => {
            cb.checked = false;
        });
        document.querySelectorAll(`#mobile-${filterType}-filter-options input[type="checkbox"]`).forEach(cb => {
            cb.checked = false;
        });

        // Nur die gewünschte Checkbox aktivieren
        const desktopCheckbox = document.querySelector(`#${filterType}-filter-options input[value="${value}"]`);
        if (desktopCheckbox) {
            desktopCheckbox.checked = true;
            console.log(`Desktop checkbox found and checked for ${value}`); // Debug
        } else {
            console.log(`Desktop checkbox NOT found for ${value}`); // Debug
        }

        const mobileCheckbox = document.querySelector(`#mobile-${filterType}-filter-options input[value="${value}"]`);
        if (mobileCheckbox) {
            mobileCheckbox.checked = true;
            console.log(`Mobile checkbox found and checked for ${value}`); // Debug
        } else {
            console.log(`Mobile checkbox NOT found for ${value}`); // Debug
        }

        // Filter-Button-Text aktualisieren
        this.updateFilterButtonText(filterType);
    }

    downloadAllVisible() {
        const visibleStorys = this.filteredStories.filter(story => !story.isHidden);

        if (visibleStorys.length === 0) {
            alert('Keine sichtbaren Geschichteer zum Herunterladen.');
            return;
        }

        // Sequenzieller Download mit kleiner Verzögerung
        visibleStorys.forEach((story, index) => {
            setTimeout(() => {
                this.downloadStory(story.id);
            }, index * 500); // 500ms Verzögerung zwischen Downloads
        });
    }

    openMobilePlayer() {
        this.modalOverlay.style.display = 'flex';
        this.miniPlayer.style.display = 'none'; // Mini-Player verstecken wenn Modal geöffnet wird

        // Event Listeners für Modal nur beim ersten Mal einrichten
        if (!this.modalEventListenersSet) {
            console.log('Setting up modal event listeners for the first time...');
            this.setupModalEventListeners();
        } else {
            console.log('Modal event listeners already set, skipping setup...');
        }

        // Tab-System zuerst initialisieren
        this.initializeModalTabs();

        // Dann Player-Zustand synchronisieren (mit kleiner Verzögerung für DOM-Bereitschaft)
        setTimeout(() => {
            if (this.currentStoryIndex !== -1) {
                const story = this.stories[this.currentStoryIndex];
                this.updateModalPlayerInfo(story);
                this.syncModalProgress();
            }

            // Immer Cover laden - entweder vom aktuellen Story oder vom ersten Story
            const storyToShowCover = this.currentStoryIndex !== -1 ?
                this.stories[this.currentStoryIndex] :
                (this.stories.length > 0 ? this.stories[0] : null);

            if (storyToShowCover) {
                this.loadModalKategorieCoverBackground(storyToShowCover.titel);
            }
        }, 100);
    }

    syncModalProgress() {
        // Synchronisiert Progress Bar und Zeit beim Öffnen des Modals
        if (!this.audio.duration) return;

        const progress = (this.audio.currentTime / this.audio.duration) * 100;
        const currentTimeText = this.formatTime(this.audio.currentTime);
        const durationText = this.formatTime(this.audio.duration);

        const modalProgressFill = document.getElementById('modal-progress-fill');
        const modalCurrentTime = document.getElementById('modal-current-time');
        const modalTotalTime = document.getElementById('modal-total-time');

        if (modalProgressFill) {
            modalProgressFill.style.width = `${progress}%`;
        }
        if (modalCurrentTime) {
            modalCurrentTime.textContent = currentTimeText;
        }
        if (modalTotalTime) {
            modalTotalTime.textContent = durationText;
        }
    }

    seekToModal(event) {
        if (!this.audio.duration) return;

        const progressContainer = event.currentTarget;
        const rect = progressContainer.getBoundingClientRect();
        const clickX = event.clientX - rect.left;
        const percentage = clickX / rect.width;
        const newTime = percentage * this.audio.duration;

        this.audio.currentTime = Math.max(0, Math.min(newTime, this.audio.duration));
    }

    closeMobilePlayer() {
        this.modalOverlay.style.display = 'none';
        // Mini-Player anzeigen wenn ein Story spielt
        if (this.currentStoryIndex !== -1) {
            this.miniPlayer.style.display = 'flex';
        }
    }

    handleResize() {
        // Mini-Player nur im mobilen Layout anzeigen
        if (window.innerWidth > 768) {
            this.miniPlayer.style.display = 'none';
        } else if (this.currentStoryIndex !== -1 && this.modalOverlay.style.display !== 'flex') {
            this.miniPlayer.style.display = 'flex';
        }
    }

    setupModalEventListeners() {
        // Event Listeners nur einmal setzen (vermeidet mehrfache Registrierung)
        if (this.modalEventListenersSet) {
            console.log('Modal event listeners already set, skipping...'); // Debug
            return;
        }

        console.log('Setting up modal event listeners...'); // Debug

        // Alle Modal-Buttons mit onclick-Attributen statt Event Listeners
        this.setupModalButtonsWithOnclick();

        // Modal Tabs
        document.querySelectorAll('.modal-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                const tabType = e.target.dataset.tab;
                this.switchModalTab(tabType);
            });
        });

        // Flag setzen, dass Event Listeners registriert wurden
        this.modalEventListenersSet = true;

        // Button-Zustände im Modal aktualisieren
        this.updateModalButtonStates();
    }

    setupModalButtonsWithOnclick() {
        // Verwende onclick-Attribute statt addEventListener für bessere Kontrolle
        const modalPlayPause = document.getElementById('modal-play-pause-btn');
        if (modalPlayPause) {
            modalPlayPause.onclick = () => {
                console.log('Modal play/pause clicked via onclick'); // Debug
                this.togglePlayPause();
            };
        }

        const modalPrev = document.getElementById('modal-prev-btn');
        if (modalPrev) {
            modalPrev.onclick = () => {
                console.log('Modal prev clicked via onclick'); // Debug
                const now = Date.now();
                if (now - this.lastSkipTime < this.skipCooldown) {
                    console.log(`Modal prev: Cooldown active (${this.skipCooldown}ms), ignoring click. Time since last: ${now - this.lastSkipTime}ms`);
                    return;
                }
                if (this.isSkipping) {
                    console.log('Skip already in progress, ignoring click');
                    return;
                }
                this.previousStory();
            };
        }

        const modalNext = document.getElementById('modal-next-btn');
        if (modalNext) {
            modalNext.onclick = () => {
                console.log('Modal next clicked via onclick'); // Debug
                const now = Date.now();
                if (now - this.lastSkipTime < this.skipCooldown) {
                    console.log(`Modal next: Cooldown active (${this.skipCooldown}ms), ignoring click. Time since last: ${now - this.lastSkipTime}ms`);
                    return;
                }
                if (this.isSkipping) {
                    console.log('Skip already in progress, ignoring click');
                    return;
                }
                this.nextStory();
            };
        }

        const modalShuffle = document.getElementById('modal-shuffle-btn');
        if (modalShuffle) {
            modalShuffle.onclick = () => this.toggleShuffle();
        }

        const modalRepeat = document.getElementById('modal-repeat-btn');
        if (modalRepeat) {
            modalRepeat.onclick = () => this.toggleRepeat();
        }

        const modalDownload = document.getElementById('modal-download-current-btn');
        if (modalDownload) {
            modalDownload.onclick = () => this.downloadCurrent();
        }

        const modalShare = document.getElementById('modal-share-btn');
        if (modalShare) {
            modalShare.onclick = (e) => this.showShareMenu(e.target);
        }

        const modalProgressContainer = document.querySelector('.modal-progress-bar-container');
        if (modalProgressContainer) {
            modalProgressContainer.onclick = (e) => this.seekToModal(e);
        }
    }

    updateModalButtonStates() {
        // Nur aktualisieren wenn Modal geöffnet ist
        if (this.modalOverlay.style.display !== 'flex') return;

        // Shuffle Button Zustand
        const modalShuffle = document.getElementById('modal-shuffle-btn');
        if (modalShuffle) {
            modalShuffle.classList.toggle('active', this.isShuffleMode);
        }

        // Repeat Button Zustand
        const modalRepeat = document.getElementById('modal-repeat-btn');
        if (modalRepeat) {
            // Alle Klassen entfernen
            modalRepeat.classList.remove('active', 'single-repeat');

            // Entsprechende Klassen setzen
            switch (this.repeatMode) {
                case 'playlist':
                    modalRepeat.classList.add('active');
                    this.setRepeatButtonContent(modalRepeat, false);
                    break;
                case 'single':
                    modalRepeat.classList.add('active', 'single-repeat');
                    this.setRepeatButtonContent(modalRepeat, true);
                    break;
                default:
                    this.setRepeatButtonContent(modalRepeat, false);
                    break;
            }
        }

        // Play/Pause Button Zustand
        const modalPlayPause = document.getElementById('modal-play-pause-btn');
        if (modalPlayPause) {
            const playIcon = modalPlayPause.querySelector('.play-icon');
            const pauseIcon = modalPlayPause.querySelector('.pause-icon');
            if (this.isPlaying) {
                if (playIcon) playIcon.style.display = 'none';
                if (pauseIcon) pauseIcon.style.display = 'block';
            } else {
                if (playIcon) playIcon.style.display = 'block';
                if (pauseIcon) pauseIcon.style.display = 'none';
            }
        }
    }

    handleAudioError(error) {
        console.error('Audio Fehler:', error);

        // Benutzerfreundliche Fehlermeldung
        const currentStory = this.stories[this.currentStoryIndex];
        const errorMessage = `Fehler beim Laden von "${currentStory?.titel || 'Unbekanntes Geschichte'}".
                             Möglicherweise ist die Audiodatei nicht verfügbar.`;

        // Fehler in der UI anzeigen statt Alert
        this.showErrorMessage(errorMessage);

        // isPlaying wird durch Audio-Events automatisch gesetzt
        this.updatePlayButton();

        // NICHT automatisch zum nächsten Geschichte springen
        // Benutzer soll selbst entscheiden, was zu tun ist
        console.log('Audio error handled, NOT auto-skipping to next story');
    }

    showErrorMessage(message) {
        // Temporäre Fehlermeldung in der Lyrics-Box anzeigen
        const originalContent = this.lyricsContent.innerHTML;
        this.lyricsContent.innerHTML = `
            <div style="color: #ff4444; padding: 10px; background: #fff5f5; border-radius: 4px; border-left: 4px solid #ff4444;">
                <strong>Fehler:</strong><br>
                ${message}
            </div>
        `;

        // Nach 5 Sekunden wieder original Inhalt anzeigen
        setTimeout(() => {
            this.lyricsContent.innerHTML = originalContent;
        }, 5000);
    }

    handleKeyboard(event) {
        // Nur reagieren wenn kein Input-Element fokussiert ist
        if (event.target.tagName === 'INPUT' || event.target.tagName === 'SELECT') return;

        switch (event.code) {
            case 'Space':
                event.preventDefault();
                this.togglePlayPause();
                break;
            case 'ArrowLeft':
                event.preventDefault();
                this.previousStory();
                break;
            case 'ArrowRight':
                event.preventDefault();
                this.nextStory();
                break;
            case 'KeyS':
                if (event.ctrlKey || event.metaKey) {
                    event.preventDefault();
                    this.toggleShuffle();
                }
                break;
            case 'KeyR':
                if (event.ctrlKey || event.metaKey) {
                    event.preventDefault();
                    this.toggleRepeat();
                }
                break;
        }
    }

    setupLyricsTabs() {
        // Event Listener für Tab-Buttons
        document.querySelectorAll('.lyrics-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                const tabType = e.target.dataset.tab;
                this.switchLyricsTab(tabType);
            });
        });
    }

    switchLyricsTab(tabType) {
        // Tab-Buttons aktualisieren
        document.querySelectorAll('.lyrics-tab').forEach(tab => {
            tab.classList.remove('active');
        });

        const targetTab = document.querySelector(`[data-tab="${tabType}"]`);
        if (targetTab) {
            targetTab.classList.add('active');
        }

        // Alle Content-Container verstecken
        this.lyricsContent.style.display = 'none';
        this.lyricsTranslation.style.display = 'none';
        this.lyricsInfo.style.display = 'none';

        // Gewählten Content anzeigen
        switch(tabType) {
            case 'lyrics':
                this.lyricsContent.style.display = 'block';
                break;
            case 'translation':
                this.lyricsTranslation.style.display = 'block';
                break;
            case 'info':
                this.lyricsInfo.style.display = 'block';
                break;
        }
    }

    hasTranslation(story) {
        // Prüft ob ein Story eine deutsche Übersetzung hat
        return story.translation && story.translation.trim();
    }

    hasInfo(story) {
        // Prüft ob ein Story zusätzliche Informationen hat (jetzt Interpretation)
        return story.interpretation && story.interpretation.trim();
    }

    updateLyricsDisplay(story) {
        // Original Lyrics anzeigen
        if (story.lyrics && story.lyrics.trim()) {
            let formattedLyrics = this.formatLyrics(story.lyrics);
            this.lyricsContent.innerHTML = `<p>${formattedLyrics}</p>`;
        } else {
            this.lyricsContent.innerHTML = '<p class="no-lyrics">Keine Lyrics verfügbar für dieses Geschichte.</p>';
        }

        // Übersetzung anzeigen (falls vorhanden)
        if (this.hasTranslation(story)) {
            let formattedTranslation = this.formatLyrics(story.translation);
            this.lyricsTranslation.innerHTML = `<p>${formattedTranslation}</p>`;
        } else {
            this.lyricsTranslation.innerHTML = '<p class="no-lyrics">Keine deutsche Übersetzung verfügbar.</p>';
        }

        // Info anzeigen (falls vorhanden) - jetzt Interpretation
        if (this.hasInfo(story)) {
            let formattedInfo = this.formatLyrics(story.interpretation);
            this.lyricsInfo.innerHTML = `<p>${formattedInfo}</p>`;
        } else {
            this.lyricsInfo.innerHTML = '<p class="no-lyrics">Keine zusätzlichen Informationen verfügbar.</p>';
        }

        // Tabs konfigurieren
        this.configureTabs(story);
    }

    configureTabs(story) {
        const hasTranslation = this.hasTranslation(story);
        const hasInfo = this.hasInfo(story);

        // Tab-Buttons anzeigen/verstecken
        const translationTab = document.querySelector('[data-tab="translation"]');
        const infoTab = document.querySelector('[data-tab="info"]');

        if (hasTranslation) {
            translationTab.style.display = 'block';
        } else {
            translationTab.style.display = 'none';
        }

        if (hasInfo) {
            infoTab.style.display = 'block';
        } else {
            infoTab.style.display = 'none';
        }

        // Tabs sind immer sichtbar (mindestens "Lyrics")
        this.lyricsTabs.style.display = 'flex';

        // Standard: Lyrics Tab aktiv
        this.switchLyricsTab('lyrics');
    }

    formatLyrics(lyrics) {
        // Lyrics formatieren: Doppelte Zeilenumbrüche = neue Absätze, einzelne = <br>
        let formatted = lyrics.trim();

        // Erst alle doppelten Zeilenumbrüche durch einen Platzhalter ersetzen
        formatted = formatted.replace(/\n\n+/g, '|||PARAGRAPH|||');

        // Dann einzelne Zeilenumbrüche durch <br> ersetzen
        formatted = formatted.replace(/\n/g, '<br>');

        // Platzhalter durch Absatz-Enden und -Anfänge ersetzen
        formatted = formatted.replace(/\|\|\|PARAGRAPH\|\|\|/g, '</p><p>');

        return formatted;
    }

    formatLyricsModern(lyrics) {
        // Moderne Formatierung für das neue Design - kleinere Absätze
        let formatted = lyrics.trim();

        // Reduziere große Abstände - doppelte Zeilenumbrüche werden zu einfachen
        formatted = formatted.replace(/\n\n+/g, '\n\n');

        // Entferne überflüssige Leerzeichen am Zeilenanfang/-ende
        formatted = formatted.split('\n').map(line => line.trim()).join('\n');

        return formatted;
    }

    // Neue Modal-Funktionen
    initializeModalTabs() {
        // Standard-Tab aktivieren
        this.switchModalTab('cover');

        // Swipe-Gesten komplett deaktivieren
        this.disableSwipeGestures();
    }

    disableSwipeGestures() {
        const tabContentArea = document.querySelector('.modal-tab-content-area');
        if (!tabContentArea) return;

        // Alle Touch-Events abfangen und verhindern (außer in scrollbaren Inhalten)
        const preventSwipe = (e) => {
            // Nur in Cover-Tab Swipes verhindern
            const activeContent = tabContentArea.querySelector('.modal-tab-content.active');
            if (activeContent && activeContent.dataset.content === 'cover') {
                e.preventDefault();
                e.stopPropagation();
            }
        };

        tabContentArea.addEventListener('touchstart', preventSwipe, { passive: false });
        tabContentArea.addEventListener('touchmove', preventSwipe, { passive: false });
        tabContentArea.addEventListener('touchend', preventSwipe, { passive: false });
    }

    switchModalTab(tabType) {
        // Tab-Buttons aktualisieren
        document.querySelectorAll('.modal-tab').forEach(tab => {
            tab.classList.remove('active');
        });

        const targetTab = document.querySelector(`.modal-tab[data-tab="${tabType}"]`);
        if (targetTab) {
            targetTab.classList.add('active');
        }

        // Tab-Content aktualisieren
        document.querySelectorAll('.modal-tab-content').forEach(content => {
            content.classList.remove('active', 'slide-left');
        });

        const targetContent = document.querySelector(`.modal-tab-content[data-content="${tabType}"]`);
        if (targetContent) {
            targetContent.classList.add('active');
        }

        // Tab-Content-Area für Cover-Tab anpassen
        const tabContentArea = document.querySelector('.modal-tab-content-area');
        if (tabContentArea) {
            if (tabType === 'cover') {
                tabContentArea.classList.add('cover-active');
            } else {
                tabContentArea.classList.remove('cover-active');
            }
        }

        // Translation-Tab nur anzeigen wenn vorhanden
        if (this.currentStoryIndex !== -1) {
            const story = this.stories[this.currentStoryIndex];

            const translationTab = document.querySelector('.modal-tab[data-tab="translation"]');
            if (translationTab) {
                translationTab.style.display = story.translation ? 'block' : 'none';
            }


        }
    }

    updateModalInfoTab(story) {
        const infoArtist = document.getElementById('modal-info-artist');
        const infoKategorie = document.getElementById('modal-info-kategorie');
        const infoLanguage = document.getElementById('modal-info-language');
        const infoInterpretation = document.getElementById('modal-info-interpretation');
        const infoInspiration = document.getElementById('modal-info-inspiration');
        const infoKindergeschichtenvideo = document.getElementById('modal-info-musikvideo');
        const infoWidmung = document.getElementById('modal-info-widmung');
        if (infoArtist) infoArtist.textContent = 'Jana Breitmar';
        if (infoKategorie) infoKategorie.textContent = story.kategorie;
        if (infoKategorie) infoKategorie.textContent = story.kategorie || '-';
        if (infoLanguage) infoLanguage.textContent = story.sprache || '-';

        // Interpretation als normaler Info-Punkt anzeigen wenn vorhanden
        if (infoInterpretation && story.interpretation) {
            infoInterpretation.style.display = 'block';
            const interpretationText = infoInterpretation.querySelector('.interpretation-text');
            if (interpretationText) {
                interpretationText.textContent = story.interpretation;
            }
        } else if (infoInterpretation) {
            infoInterpretation.style.display = 'none';
        }

        // Inspiration als normaler Info-Punkt anzeigen wenn vorhanden
        if (infoInspiration && story.inspiration) {
            infoInspiration.style.display = 'block';
            const inspirationText = infoInspiration.querySelector('.inspiration-text');
            if (inspirationText) {
                inspirationText.textContent = story.inspiration;
            }
        } else if (infoInspiration) {
            infoInspiration.style.display = 'none';
        }

        // Kindergeschichtenvideo als normaler Info-Punkt anzeigen wenn vorhanden
        if (infoKindergeschichtenvideo && story.musikvideo) {
            infoKindergeschichtenvideo.style.display = 'block';
            const musikvideoLink = infoKindergeschichtenvideo.querySelector('.musikvideo-link');
            if (musikvideoLink) {
                musikvideoLink.href = story.musikvideo;
            }
        } else if (infoKindergeschichtenvideo) {
            infoKindergeschichtenvideo.style.display = 'none';
        }

        // Widmung als normaler Info-Punkt anzeigen wenn vorhanden
        if (infoWidmung && story.widmung) {
            infoWidmung.style.display = 'block';
            const widmungText = infoWidmung.querySelector('.widmung-text');
            if (widmungText) {
                widmungText.textContent = story.widmung;
            }
        } else if (infoWidmung) {
            infoWidmung.style.display = 'none';
        }

        // Event Listener für klickbare Info-Werte neu registrieren
        this.setupInfoClickListeners();
    }

    setupInfoClickListeners() {
        // Alte Event Listener entfernen (falls vorhanden)
        document.querySelectorAll('.info-value.clickable').forEach(element => {
            element.replaceWith(element.cloneNode(true));
        });

        // Neue Event Listener hinzufügen
        document.querySelectorAll('.info-value.clickable').forEach(element => {
            element.addEventListener('click', (e) => {
                const filterType = e.target.dataset.filterType;
                const filterValue = e.target.textContent.trim();

                console.log(`Info click: ${filterType} = ${filterValue}`); // Debug

                if (filterValue && filterValue !== '-') {
                    this.filterByInfoValue(filterType, filterValue);
                }
            });
        });
    }

    updateModalLyricsTabs(story) {
        // Lyrics Content aktualisieren
        const lyricsContent = document.getElementById('modal-lyrics-content');
        if (lyricsContent) {
            if (story.lyrics) {
                lyricsContent.innerHTML = `<h3 class="tab-content-title">Lyrics</h3><p>${this.formatLyricsModern(story.lyrics)}</p>`;
            } else {
                lyricsContent.innerHTML = '<h3 class="tab-content-title">Lyrics</h3><p class="no-lyrics">Keine Lyrics verfügbar.</p>';
            }
        }

        // Translation Content aktualisieren
        const translationContent = document.getElementById('modal-lyrics-translation');
        if (translationContent) {
            if (story.translation) {
                translationContent.innerHTML = `<h3 class="tab-content-title">Übersetzung</h3><p>${this.formatLyricsModern(story.translation)}</p>`;
            } else {
                translationContent.innerHTML = '<h3 class="tab-content-title">Übersetzung</h3><p class="no-lyrics">Keine Übersetzung verfügbar.</p>';
            }
        }

        // Translation Tab sichtbar machen wenn vorhanden
        const translationTab = document.querySelector('.modal-tab[data-tab="translation"]');
        if (translationTab) {
            translationTab.style.display = story.translation ? 'block' : 'none';
        }


    }

    // Desktop Sidebar Funktionen
    setupDesktopEventListeners() {
        if (this.desktopEventListenersSet) return;

        // Desktop Control Buttons
        const desktopPlayPauseBtn = document.getElementById('desktop-play-pause-btn');
        if (desktopPlayPauseBtn) {
            desktopPlayPauseBtn.addEventListener('click', () => this.togglePlayPause());
        }

        const desktopPrevBtn = document.getElementById('desktop-prev-btn');
        if (desktopPrevBtn) {
            desktopPrevBtn.addEventListener('click', () => this.previousStory());
        }

        const desktopNextBtn = document.getElementById('desktop-next-btn');
        if (desktopNextBtn) {
            desktopNextBtn.addEventListener('click', () => this.nextStory());
        }

        const desktopShuffleBtn = document.getElementById('desktop-shuffle-btn');
        if (desktopShuffleBtn) {
            desktopShuffleBtn.addEventListener('click', () => this.toggleShuffle());
        }

        const desktopRepeatBtn = document.getElementById('desktop-repeat-btn');
        if (desktopRepeatBtn) {
            desktopRepeatBtn.addEventListener('click', () => this.toggleRepeat());
        }

        const desktopDownloadBtn = document.getElementById('desktop-download-current-btn');
        if (desktopDownloadBtn) {
            desktopDownloadBtn.addEventListener('click', () => {
                if (this.currentStoryIndex !== -1) {
                    this.downloadStory(this.stories[this.currentStoryIndex].id);
                }
            });
        }

        const desktopShareBtn = document.getElementById('desktop-share-btn');
        if (desktopShareBtn) {
            desktopShareBtn.addEventListener('click', (e) => this.showShareMenu(e.target));
        }

        // Desktop Progress Bar
        const desktopProgressBar = document.getElementById('desktop-progress-bar');
        if (desktopProgressBar) {
            desktopProgressBar.addEventListener('click', (e) => this.seekToDesktop(e));
        }

        // Desktop Tabs
        document.querySelectorAll('.desktop-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                const tabType = e.target.dataset.tab;
                this.switchDesktopTab(tabType);
            });
        });

        this.desktopEventListenersSet = true;
    }

    initializeDesktopSidebar() {
        // Desktop Sidebar komplett initialisieren
        this.setupDesktopEventListeners();
        this.initializeDesktopTabs();

        // Platzhalter-Zustand anzeigen (kein spezifisches Geschichte)
        this.showDesktopPlaceholder();
    }

    showDesktopPlaceholder() {
        // Desktop Player Info auf Platzhalter setzen
        const desktopStoryTitle = document.getElementById('desktop-story-title');
        const desktopStoryArtist = document.getElementById('desktop-story-artist');
        const desktopStoryKategorie = document.getElementById('desktop-info-category');
        const desktopStoryAltersgruppe = document.getElementById('desktop-info-age');
        const desktopStorySprache = document.getElementById('desktop-story-sprache');

        if (desktopStoryTitle) desktopStoryTitle.textContent = 'Wähle ein Geschichte aus';
        if (desktopStoryArtist) desktopStoryArtist.textContent = 'Jana Breitmar';
        if (desktopStoryKategorie) desktopStoryKategorie.textContent = '';
        if (desktopStoryKategorie) desktopStoryKategorie.textContent = '';
        if (desktopStorySprache) desktopStorySprache.textContent = '';

        // Platzhalter-Cover laden
        this.loadDesktopPlaceholderCover();

        // Lyrics und andere Tabs leeren
        const desktopLyricsContent = document.getElementById('desktop-lyrics-content');
        if (desktopLyricsContent) {
            desktopLyricsContent.innerHTML = '<p class="no-lyrics">Wähle ein Geschichte aus, um die Lyrics anzuzeigen.</p>';
        }
    }

    loadDesktopPlaceholderCover() {
        const desktopCoverTab = document.querySelector('.desktop-tab-content[data-content="cover"]');
        if (desktopCoverTab) {
            // Platzhalter-Cover setzen
            desktopCoverTab.style.backgroundImage = 'url("images/kategorie-cover-placeholder.jpg")';
            desktopCoverTab.style.backgroundSize = 'cover';
            desktopCoverTab.style.backgroundPosition = 'center';
            desktopCoverTab.style.backgroundRepeat = 'no-repeat';
            desktopCoverTab.classList.add('has-cover');
        }
    }

    initializeDesktopTabs() {
        // Desktop Tab System initialisieren
        this.switchDesktopTab('cover');
    }

    updateDesktopPlayerInfo(story) {
        // Desktop Story Title aktualisieren
        const desktopStoryTitle = document.getElementById('desktop-story-title');
        if (desktopStoryTitle) {
            desktopStoryTitle.textContent = story.titel;
        }

        // Desktop Lyrics und Info aktualisieren
        this.updateDesktopLyricsTabs(story);
        this.updateDesktopInfoTab(story);
    }

    syncDesktopProgress() {
        // Desktop Progress Bar synchronisieren
        if (!this.audio.duration) return;

        const currentTime = this.audio.currentTime;
        const duration = this.audio.duration;
        const percentage = (currentTime / duration) * 100;

        const desktopProgressFill = document.getElementById('desktop-progress-fill');
        const desktopProgressHandle = document.getElementById('desktop-progress-handle');
        const desktopCurrentTime = document.getElementById('desktop-current-time');
        const desktopTotalTime = document.getElementById('desktop-total-time');

        if (desktopProgressFill) {
            desktopProgressFill.style.width = `${percentage}%`;
        }

        if (desktopProgressHandle) {
            desktopProgressHandle.style.left = `${percentage}%`;
        }

        if (desktopCurrentTime) {
            desktopCurrentTime.textContent = this.formatTime(currentTime);
        }

        if (desktopTotalTime) {
            desktopTotalTime.textContent = this.formatTime(duration);
        }
    }

    loadDesktopKategorieCoverBackground(storyTitle) {
        // Funktion mit Retry-Mechanismus für robustere Ausführung
        const attemptLoad = (retryCount = 0) => {
            const coverTab = document.querySelector('.desktop-tab-content[data-content="cover"]');

            if (!coverTab) {
                // Wenn Element nicht gefunden und noch Retries übrig, erneut versuchen
                if (retryCount < 3) {
                    setTimeout(() => attemptLoad(retryCount + 1), 100);
                }
                return;
            }

            // Für Kindergeschichten das quadratische Cover verwenden
            if (storyTitle === "Tennet und die magische Blume") {
                const coverPath = 'bilder/kindergeschichten/Tennet und die magische Blume-quadrat.png';
                const img = new Image();
                img.onload = () => {
                    coverTab.classList.add('has-cover');
                    coverTab.innerHTML = `<img src="${coverPath}" alt="Story Cover">`;
                };
                img.onerror = () => {
                    // Fallback zu Standard-Verhalten
                    this.loadStandardDesktopCover(coverTab, storyTitle);
                };
                img.src = coverPath;
                return;
            }

            // Standard-Verhalten für andere Stories
            this.loadStandardDesktopCover(coverTab, storyTitle);
        };

        attemptLoad();
    }

    loadStandardDesktopCover(coverTab, storyTitle) {
        const possibleFormats = ['png', 'jpg', 'jpeg', 'webp'];
        let formatIndex = 0;

        const tryLoadImage = () => {
            if (formatIndex >= possibleFormats.length) {
                // Kein Bild gefunden - Platzhalter anzeigen
                coverTab.style.backgroundImage = '';
                coverTab.classList.remove('has-cover');
                coverTab.innerHTML = `
                    <svg class="placeholder-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/>
                    </svg>
                `;
                return;
            }

            const format = possibleFormats[formatIndex];
            const coverFilename = this.getCoverFilename(storyTitle);
            const coverPath = `bilder/kategorie-covers/${coverFilename}.${format}`;

            const img = new Image();
            img.onload = () => {
                // Bild gefunden - als IMG-Element setzen (wie im Modal)
                coverTab.classList.add('has-cover');
                coverTab.innerHTML = `<img src="${coverPath}" alt="Kategorie Cover">`;
            };

            img.onerror = () => {
                // Nächstes Format versuchen
                formatIndex++;
                tryLoadImage();
            };

            img.src = coverPath;
        };

        tryLoadImage();
    }

    seekToDesktop(event) {
        if (!this.audio.duration) return;

        const progressContainer = event.currentTarget;
        const rect = progressContainer.getBoundingClientRect();
        const clickX = event.clientX - rect.left;
        const percentage = clickX / rect.width;
        const newTime = percentage * this.audio.duration;

        this.audio.currentTime = Math.max(0, Math.min(newTime, this.audio.duration));
    }

    switchDesktopTab(tabType) {
        // Desktop Tab Content umschalten
        document.querySelectorAll('.desktop-tab-content').forEach(content => {
            content.classList.remove('active');
        });

        document.querySelectorAll('.desktop-tab').forEach(tab => {
            tab.classList.remove('active');
        });

        const targetContent = document.querySelector(`.desktop-tab-content[data-content="${tabType}"]`);
        const targetTab = document.querySelector(`.desktop-tab[data-tab="${tabType}"]`);

        if (targetContent) targetContent.classList.add('active');
        if (targetTab) targetTab.classList.add('active');

        // Tab Indicator Position aktualisieren
        this.updateDesktopTabIndicator(targetTab);
    }

    updateDesktopTabIndicator(activeTab) {
        const indicator = document.querySelector('.desktop-tab-indicator');
        if (!indicator || !activeTab) return;

        const tabRect = activeTab.getBoundingClientRect();
        const containerRect = activeTab.parentElement.getBoundingClientRect();
        const left = tabRect.left - containerRect.left;
        const width = tabRect.width;

        indicator.style.left = `${left}px`;
        indicator.style.width = `${width}px`;
    }

    updateDesktopLyricsTabs(story) {
        // Desktop Lyrics Content aktualisieren
        const lyricsContent = document.getElementById('desktop-lyrics-content');
        if (lyricsContent) {
            if (story.lyrics) {
                lyricsContent.innerHTML = `<h3 class="tab-content-title">Lyrics</h3><p>${this.formatLyricsModern(story.lyrics)}</p>`;
            } else {
                lyricsContent.innerHTML = '<h3 class="tab-content-title">Lyrics</h3><p class="no-lyrics">Keine Lyrics verfügbar.</p>';
            }
        }

        // Desktop Translation Content aktualisieren
        const translationContent = document.getElementById('desktop-lyrics-translation');
        if (translationContent) {
            if (story.translation) {
                translationContent.innerHTML = `<h3 class="tab-content-title">Übersetzung</h3><p>${this.formatLyricsModern(story.translation)}</p>`;
            } else {
                translationContent.innerHTML = '<h3 class="tab-content-title">Übersetzung</h3><p class="no-lyrics">Keine Übersetzung verfügbar.</p>';
            }
        }

        // Desktop Translation Tab sichtbar machen wenn vorhanden
        const translationTab = document.querySelector('.desktop-tab[data-tab="translation"]');
        if (translationTab) {
            translationTab.style.display = story.translation ? 'block' : 'none';
        }
    }

    updateDesktopInfoTab(story) {
        // Desktop Info Tab aktualisieren
        const desktopInfoArtist = document.getElementById('desktop-info-artist');
        const desktopInfoKategorie = document.getElementById('desktop-info-category');
        const desktopInfoAltersgruppe = document.getElementById('desktop-info-age');
        const desktopInfoLanguage = document.getElementById('desktop-info-language');
        const desktopInfoInterpretation = document.getElementById('desktop-info-interpretation');
        const desktopInfoInspiration = document.getElementById('desktop-info-inspiration');
        const desktopInfoKindergeschichtenvideo = document.getElementById('desktop-info-musikvideo');
        const desktopInfoWidmung = document.getElementById('desktop-info-widmung');
        if (desktopInfoArtist) desktopInfoArtist.textContent = 'Jana Breitmar';
        if (desktopInfoKategorie) desktopInfoKategorie.textContent = story.kategorie;
        if (desktopInfoKategorie) desktopInfoKategorie.textContent = story.kategorie;
        if (desktopInfoLanguage) desktopInfoLanguage.textContent = story.sprache;

        if (desktopInfoInterpretation) {
            if (story.interpretation) {
                desktopInfoInterpretation.style.display = 'block';
                const interpretationText = desktopInfoInterpretation.querySelector('.interpretation-text');
                if (interpretationText) {
                    interpretationText.textContent = story.interpretation;
                }
            } else {
                desktopInfoInterpretation.style.display = 'none';
            }
        }

        if (desktopInfoInspiration) {
            if (story.inspiration) {
                desktopInfoInspiration.style.display = 'block';
                const inspirationText = desktopInfoInspiration.querySelector('.inspiration-text');
                if (inspirationText) {
                    inspirationText.textContent = story.inspiration;
                }
            } else {
                desktopInfoInspiration.style.display = 'none';
            }
        }

        if (desktopInfoKindergeschichtenvideo) {
            if (story.musikvideo) {
                desktopInfoKindergeschichtenvideo.style.display = 'block';
                const musikvideoLink = desktopInfoKindergeschichtenvideo.querySelector('.musikvideo-link');
                if (musikvideoLink) {
                    musikvideoLink.href = story.musikvideo;
                }
            } else {
                desktopInfoKindergeschichtenvideo.style.display = 'none';
            }
        }

        if (desktopInfoWidmung) {
            if (story.widmung) {
                desktopInfoWidmung.style.display = 'block';
                const widmungText = desktopInfoWidmung.querySelector('.widmung-text');
                if (widmungText) {
                    widmungText.textContent = story.widmung;
                }
            } else {
                desktopInfoWidmung.style.display = 'none';
            }
        }
    }

    // Swipe-Gesten für Tabs entfernt - nur noch Touch-Navigation über Tab-Buttons

    // Share-Funktionalität
    generateShareUrl(storyTitle, storyId = null) {
        const baseUrl = window.location.origin + window.location.pathname;

        // Bevorzuge Story-ID für bessere URL-Verknüpfung, fallback auf Titel
        if (storyId) {
            return `${baseUrl}?play=${storyId}`;
        } else {
            const encodedTitle = encodeURIComponent(storyTitle);
            return `${baseUrl}?play=${encodedTitle}`;
        }
    }

    shareToWhatsApp() {
        if (this.currentStoryIndex === -1) {
            alert('Bitte wähle zuerst ein Geschichte aus.');
            return;
        }

        const currentStory = this.stories[this.currentStoryIndex];
        const shareUrl = this.generateShareUrl(currentStory.titel, currentStory.id);
        const message = `Hör dir "${currentStory.titel}" von Jana Breitmar an: ${shareUrl}`;
        const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(message)}`;

        window.open(whatsappUrl, '_blank');
    }

    async copyShareLink() {
        if (this.currentStoryIndex === -1) {
            alert('Bitte wähle zuerst ein Geschichte aus.');
            return;
        }

        const currentStory = this.stories[this.currentStoryIndex];
        const shareUrl = this.generateShareUrl(currentStory.titel, currentStory.id);

        try {
            await navigator.clipboard.writeText(shareUrl);

            // Feedback anzeigen
            this.showShareFeedback('Link kopiert!');
        } catch (err) {
            // Fallback für ältere Browser
            const textArea = document.createElement('textarea');
            textArea.value = shareUrl;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);

            this.showShareFeedback('Link kopiert!');
        }
    }

    showShareMenu(buttonElement) {
        if (this.currentStoryIndex === -1) {
            alert('Bitte wähle zuerst ein Geschichte aus.');
            return;
        }

        // Prüfen ob bereits ein Share-Menü existiert
        const existingMenu = document.querySelector('.share-menu');
        if (existingMenu) {
            existingMenu.remove();
            return;
        }

        // Share-Menü erstellen
        const shareMenu = document.createElement('div');
        shareMenu.className = 'share-menu';
        shareMenu.innerHTML = `
            <div class="share-menu-content">
                <button class="share-option" data-action="whatsapp">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.515z"/>
                    </svg>
                    WhatsApp
                </button>
                <button class="share-option" data-action="copy">
                    <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <rect x="9" y="9" width="13" height="13" rx="2" ry="2"/>
                        <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                    </svg>
                    Link kopieren
                </button>
            </div>
        `;

        // Position relativ zum Button berechnen
        const buttonRect = buttonElement.getBoundingClientRect();
        shareMenu.style.position = 'fixed';
        shareMenu.style.top = `${buttonRect.top - 120}px`;
        shareMenu.style.left = `${buttonRect.left - 50}px`;
        shareMenu.style.zIndex = '10000';

        document.body.appendChild(shareMenu);

        // Event Listeners für Share-Optionen
        shareMenu.querySelector('[data-action="whatsapp"]').addEventListener('click', () => {
            this.shareToWhatsApp();
            shareMenu.remove();
        });

        shareMenu.querySelector('[data-action="copy"]').addEventListener('click', () => {
            this.copyShareLink();
            shareMenu.remove();
        });

        // Menü schließen wenn außerhalb geklickt wird
        setTimeout(() => {
            document.addEventListener('click', (e) => {
                if (!shareMenu.contains(e.target) && e.target !== buttonElement) {
                    shareMenu.remove();
                }
            }, { once: true });
        }, 100);
    }

    showShareFeedback(message) {
        // Feedback-Element erstellen
        const feedback = document.createElement('div');
        feedback.className = 'share-feedback';
        feedback.textContent = message;
        feedback.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 12px 24px;
            border-radius: 8px;
            z-index: 10001;
            font-size: 14px;
            pointer-events: none;
        `;

        document.body.appendChild(feedback);

        // Nach 2 Sekunden entfernen
        setTimeout(() => {
            feedback.remove();
        }, 2000);
    }
}

// Player initialisieren wenn DOM geladen ist
document.addEventListener('DOMContentLoaded', () => {
    console.log('Initialisiere KindergeschichtenPlayer...');
    try {
        window.kindergeschichtenPlayer = new KindergeschichtenPlayer();
        console.log('KindergeschichtenPlayer erfolgreich initialisiert');
    } catch (error) {
        console.error('Fehler beim Initialisieren des KindergeschichtenPlayers:', error);
    }
});

