<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kindergeschichten - Jana <PERSON>it<PERSON></title>
    <link rel="icon" type="image/png" href="bilder/favicon.png">
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="kindergeschichten-styles.css">
</head>
<body>
    <!-- Header wird durch header-and-footer.js eingefügt -->
    
    <!-- Main Content -->
    <main class="kindergeschichten-main">
        <div class="container">
            <header class="kindergeschichten-header">
                <h1 class="kindergeschichten-title">Kindergeschichten</h1>
            </header>

            <!-- Mobile Layout -->
            <div class="mobile-layout">
                <!-- Story List Container -->
                <div class="story-list-container">
                    <!-- Mobile Filter Toggle Button (ausgeblendet für jetzt) -->
                    <div class="mobile-filter-toggle-container" style="display: none;">
                        <button id="mobile-filter-toggle" class="mobile-filter-toggle-btn">
                            <span>Geschichten filtern</span>
                            <svg class="filter-toggle-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="6,9 12,15 18,9"/>
                            </svg>
                        </button>
                    </div>

                <!-- Mobile Filter Controls (initially hidden) -->
                <div class="mobile-filter-controls" id="mobile-filter-controls" style="display: none;">
                    <!-- Filter für später -->
                </div>

                <!-- Mobile Control Buttons -->
                <div class="mobile-control-buttons">
                    <button class="mobile-control-btn" id="mobile-play-filtered-btn" title="Wiedergabe">
                        <svg class="play-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M8 5v14l11-7z"/>
                        </svg>
                        <svg class="pause-icon" viewBox="0 0 24 24" fill="currentColor" style="display: none;">
                            <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
                        </svg>
                    </button>
                    <button class="mobile-control-btn" id="mobile-prev-btn" title="Vorherige Geschichte">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M6 6h2v12H6zm3.5 6l8.5 6V6z"/>
                        </svg>
                    </button>
                    <button class="mobile-control-btn" id="mobile-next-btn" title="Nächste Geschichte">
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M6 18l8.5-6L6 6v12zM16 6v12h2V6h-2z"/>
                        </svg>
                    </button>
                    <button class="mobile-control-btn" id="mobile-shuffle-btn" title="Zufällige Wiedergabe">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="16,3 21,3 21,8"/>
                            <path d="M4 20L21 3"/>
                            <polyline points="21,16 21,21 16,21"/>
                            <path d="M15 15l6 6"/>
                            <path d="M4 4l5 5"/>
                        </svg>
                    </button>
                    <button class="mobile-control-btn" id="mobile-repeat-btn" title="Wiederholen">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="17,1 21,5 17,9"/>
                            <path d="M3 11V9a4 4 0 0 1 4-4h14"/>
                            <polyline points="7,23 3,19 7,15"/>
                            <path d="M21 13v2a4 4 0 0 1-4 4H3"/>
                        </svg>
                    </button>
                    <button class="mobile-control-btn" id="mobile-download-filtered-btn" title="Geschichte als PDF herunterladen">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                            <polyline points="7,10 12,15 17,10"/>
                            <line x1="12" y1="15" x2="12" y2="3"/>
                        </svg>
                    </button>
                </div>

                    <!-- Filter Status Display -->
                    <div class="filter-status" id="filter-status" style="display: none;">
                        <span class="filter-status-text" id="filter-status-text"></span>
                        <button class="filter-status-reset" id="filter-status-reset" title="Filter zurücksetzen">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <line x1="18" y1="6" x2="6" y2="18"/>
                                <line x1="6" y1="6" x2="18" y2="18"/>
                            </svg>
                        </button>
                    </div>

                    <!-- Mobile Story List -->
                    <div class="mobile-story-list" id="mobile-story-list">
                        <!-- Stories werden hier dynamisch eingefügt -->
                    </div>
                </div> <!-- Ende story-list-container -->

                <!-- Desktop Sidebar (nur auf Desktop sichtbar) -->
                <div class="desktop-sidebar" id="desktop-sidebar">
                    <!-- Desktop Player Content -->
                    <div class="desktop-player-content">
                        <!-- Upper Section: PDF Preview with Tab Content -->
                        <div class="desktop-upper-section">
                            <!-- Tab Content Area -->
                            <div class="desktop-tab-content-area">
                                <!-- PDF Preview Tab -->
                                <div class="desktop-tab-content active" data-content="preview">
                                    <!-- PDF Preview wird als Hintergrund gesetzt -->
                                </div>

                                <!-- Story Text Tab -->
                                <div class="desktop-tab-content" data-content="text" id="desktop-text-content">
                                    <p class="no-text">Wähle eine Geschichte aus, um den Text anzuzeigen.</p>
                                </div>

                                <!-- Info Tab -->
                                <div class="desktop-tab-content" data-content="info" id="desktop-info-content">
                                    <div class="info-container">
                                        <h3 class="tab-content-title">Info</h3>

                                        <div class="info-item">
                                            <span class="info-label">Autorin:</span>
                                            <span class="info-value" id="desktop-info-author">Jana Breitmar</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">Kategorie:</span>
                                            <span class="info-value clickable" id="desktop-info-category" data-filter-type="kategorie">-</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">Altersgruppe:</span>
                                            <span class="info-value clickable" id="desktop-info-age" data-filter-type="altersgruppe">-</span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">Sprache:</span>
                                            <span class="info-value clickable" id="desktop-info-language" data-filter-type="sprache">-</span>
                                        </div>

                                        <!-- Beschreibung als Info-Item -->
                                        <div class="info-item" id="desktop-info-description">
                                            <span class="info-label">Beschreibung:</span>
                                            <span class="info-value description-text">-</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Tab Navigation -->
                            <div class="desktop-tab-navigation">
                                <div class="desktop-tab-indicator"></div>
                                <button class="desktop-tab active" data-tab="preview">Vorschau</button>
                                <button class="desktop-tab" data-tab="text">Text</button>
                                <button class="desktop-tab" data-tab="info">Info</button>
                            </div>
                        </div>

                        <!-- Lower Section: Player Controls -->
                        <div class="desktop-lower-section">
                            <!-- Story Title -->
                            <div class="desktop-story-title-container">
                                <h3 class="desktop-story-title" id="desktop-story-title">Wähle eine Geschichte aus</h3>
                                <p class="desktop-story-author" id="desktop-story-author">Jana Breitmar</p>
                            </div>

                            <!-- Player Controls -->
                            <div class="desktop-player-controls">
                                <div class="desktop-control-buttons">
                                    <button class="desktop-control-btn" id="desktop-prev-btn" title="Vorherige Geschichte">
                                        <svg viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M6 6h2v12H6zm3.5 6l8.5 6V6z"/>
                                        </svg>
                                    </button>
                                    <button class="desktop-control-btn desktop-play-pause-btn" id="desktop-play-pause-btn" title="Abspielen/Pausieren">
                                        <svg class="play-icon" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M8 5v14l11-7z"/>
                                        </svg>
                                        <svg class="pause-icon" viewBox="0 0 24 24" fill="currentColor" style="display: none;">
                                            <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
                                        </svg>
                                    </button>
                                    <button class="desktop-control-btn" id="desktop-next-btn" title="Nächste Geschichte">
                                        <svg viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M6 18l8.5-6L6 6v12zM16 6v12h2V6h-2z"/>
                                        </svg>
                                    </button>
                                </div>

                                <div class="desktop-progress-container">
                                    <span class="desktop-time-display" id="desktop-current-time">0:00</span>
                                    <div class="desktop-progress-bar-container">
                                        <div class="desktop-progress-bar" id="desktop-progress-bar">
                                            <div class="desktop-progress-fill" id="desktop-progress-fill"></div>
                                            <div class="desktop-progress-handle" id="desktop-progress-handle"></div>
                                        </div>
                                    </div>
                                    <span class="desktop-time-display" id="desktop-total-time">0:00</span>
                                </div>

                                <div class="desktop-additional-controls">
                                    <button class="desktop-control-btn" id="desktop-shuffle-btn" title="Zufällige Wiedergabe">
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <polyline points="16,3 21,3 21,8"/>
                                            <path d="M4 20L21 3"/>
                                            <polyline points="21,16 21,21 16,21"/>
                                            <path d="M15 15l6 6"/>
                                            <path d="M4 4l5 5"/>
                                        </svg>
                                    </button>
                                    <button class="desktop-control-btn" id="desktop-repeat-btn" title="Wiederholen">
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <polyline points="17,1 21,5 17,9"/>
                                            <path d="M3 11V9a4 4 0 0 1 4-4h14"/>
                                            <polyline points="7,23 3,19 7,15"/>
                                            <path d="M21 13v2a4 4 0 0 1-4 4H3"/>
                                        </svg>
                                    </button>
                                    <button class="desktop-control-btn" id="desktop-download-current-btn" title="Aktuelle Geschichte als PDF herunterladen">
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                                            <polyline points="7,10 12,15 17,10"/>
                                            <line x1="12" y1="15" x2="12" y2="3"/>
                                        </svg>
                                    </button>
                                    <button class="desktop-control-btn" id="desktop-share-btn" title="Aktuelle Geschichte teilen">
                                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                            <circle cx="18" cy="5" r="3"/>
                                            <circle cx="6" cy="12" r="3"/>
                                            <circle cx="18" cy="19" r="3"/>
                                            <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"/>
                                            <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"/>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Mini Player (wird am unteren Rand angezeigt) -->
                <div class="mini-player" id="mini-player" style="display: none;">
                    <div class="mini-player-info" id="mini-player-info">
                        <div class="mini-story-cover">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z"/>
                            </svg>
                        </div>
                        <div class="mini-story-info">
                            <span class="mini-story-title" id="mini-story-title">Keine Geschichte ausgewählt</span>
                            <span class="mini-author">Jana Breitmar</span>
                        </div>
                    </div>
                    <div class="mini-controls">
                        <button class="mini-control-btn" id="mini-play-pause" title="Abspielen/Pausieren">
                            <svg class="play-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M8 5v14l11-7z"/>
                            </svg>
                            <svg class="pause-icon" viewBox="0 0 24 24" fill="currentColor" style="display: none;">
                                <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
                            </svg>
                        </button>
                        <button class="mini-control-btn" id="mini-expand" title="Player öffnen">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="15,3 21,3 21,9"/>
                                <polyline points="9,21 3,21 3,15"/>
                                <line x1="21" y1="3" x2="14" y2="10"/>
                                <line x1="3" y1="21" x2="10" y2="14"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Mobile Player Modal -->
    <div class="modal-overlay" id="modal-overlay" style="display: none;">
        <div class="modal-player" id="modal-player">
            <!-- Close Button -->
            <button class="modal-close" id="modal-close">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="18" y1="6" x2="6" y2="18"/>
                    <line x1="6" y1="6" x2="18" y2="18"/>
                </svg>
            </button>

            <!-- Upper Half: PDF Preview with Tab Content -->
            <div class="modal-upper-section">
                <!-- Tab Content Area -->
                <div class="modal-tab-content-area">
                    <!-- PDF Preview Tab -->
                    <div class="modal-tab-content active" data-content="preview">
                        <!-- PDF Preview wird als Hintergrund gesetzt -->
                    </div>

                    <!-- Story Text Tab -->
                    <div class="modal-tab-content" data-content="text" id="modal-text-content">
                        <p class="no-text">Wähle eine Geschichte aus, um den Text anzuzeigen.</p>
                    </div>

                    <!-- Info Tab -->
                    <div class="modal-tab-content" data-content="info" id="modal-info-content">
                        <div class="info-container">
                            <h3 class="tab-content-title">Info</h3>

                            <div class="info-item">
                                <span class="info-label">Autorin:</span>
                                <span class="info-value" id="modal-info-author">Jana Breitmar</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Kategorie:</span>
                                <span class="info-value clickable" id="modal-info-category" data-filter-type="kategorie">-</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Altersgruppe:</span>
                                <span class="info-value clickable" id="modal-info-age" data-filter-type="altersgruppe">-</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Sprache:</span>
                                <span class="info-value clickable" id="modal-info-language" data-filter-type="sprache">-</span>
                            </div>

                            <!-- Beschreibung als Info-Item -->
                            <div class="info-item" id="modal-info-description">
                                <span class="info-label">Beschreibung:</span>
                                <span class="info-value description-text">-</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tab Navigation -->
                <div class="modal-tab-navigation">
                    <div class="modal-tab-indicator"></div>
                    <button class="modal-tab active" data-tab="preview">Vorschau</button>
                    <button class="modal-tab" data-tab="text">Text</button>
                    <button class="modal-tab" data-tab="info">Info</button>
                </div>
            </div>

            <!-- Lower Half: Player Controls -->
            <div class="modal-lower-section">
                <!-- Story Title -->
                <div class="modal-story-title-container">
                    <h3 class="modal-story-title" id="modal-story-title">Wähle eine Geschichte aus</h3>
                    <p class="modal-story-author" id="modal-story-author">Jana Breitmar</p>
                </div>

                <!-- Player Controls -->
                <div class="modal-player-controls">
                    <div class="modal-control-buttons">
                        <button class="modal-control-btn" id="modal-prev-btn" title="Vorherige Geschichte">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M6 6h2v12H6zm3.5 6l8.5 6V6z"/>
                            </svg>
                        </button>
                        <button class="modal-control-btn modal-play-pause-btn" id="modal-play-pause-btn" title="Abspielen/Pausieren">
                            <svg class="play-icon" viewBox="0 0 24 24" fill="currentColor">
                                <path d="M8 5v14l11-7z"/>
                            </svg>
                            <svg class="pause-icon" viewBox="0 0 24 24" fill="currentColor" style="display: none;">
                                <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
                            </svg>
                        </button>
                        <button class="modal-control-btn" id="modal-next-btn" title="Nächste Geschichte">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M6 18l8.5-6L6 6v12zM16 6v12h2V6h-2z"/>
                            </svg>
                        </button>
                    </div>

                    <div class="modal-progress-container">
                        <span class="modal-time-display" id="modal-current-time">0:00</span>
                        <div class="modal-progress-bar-container">
                            <div class="modal-progress-bar" id="modal-progress-bar">
                                <div class="modal-progress-fill" id="modal-progress-fill"></div>
                                <div class="modal-progress-handle" id="modal-progress-handle"></div>
                            </div>
                        </div>
                        <span class="modal-time-display" id="modal-total-time">0:00</span>
                    </div>

                    <div class="modal-additional-controls">
                        <button class="modal-control-btn" id="modal-shuffle-btn" title="Zufällige Wiedergabe">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="16,3 21,3 21,8"/>
                                <path d="M4 20L21 3"/>
                                <polyline points="21,16 21,21 16,21"/>
                                <path d="M15 15l6 6"/>
                                <path d="M4 4l5 5"/>
                            </svg>
                        </button>
                        <button class="modal-control-btn" id="modal-repeat-btn" title="Wiederholen">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <polyline points="17,1 21,5 17,9"/>
                                <path d="M3 11V9a4 4 0 0 1 4-4h14"/>
                                <polyline points="7,23 3,19 7,15"/>
                                <path d="M21 13v2a4 4 0 0 1-4 4H3"/>
                            </svg>
                        </button>
                        <button class="modal-control-btn" id="modal-download-current-btn" title="Aktuelle Geschichte als PDF herunterladen">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                                <polyline points="7,10 12,15 17,10"/>
                                <line x1="12" y1="15" x2="12" y2="3"/>
                            </svg>
                        </button>
                        <button class="modal-control-btn" id="modal-share-btn" title="Aktuelle Geschichte teilen">
                            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="18" cy="5" r="3"/>
                                <circle cx="6" cy="12" r="3"/>
                                <circle cx="18" cy="19" r="3"/>
                                <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"/>
                                <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Audio Element -->
    <audio id="audio-player" preload="metadata"></audio>

    <!-- Scripts -->
    <script src="header-and-footer.js"></script>
    <script src="kindergeschichten-data.js"></script>
    <script>
        // Einfache manuelle Story-Liste
        document.addEventListener('DOMContentLoaded', () => {
            console.log('DOM geladen - Füge Stories manuell hinzu');
            const storyList = document.getElementById('mobile-story-list');

            if (storyList && typeof KINDERGESCHICHTEN_DATEN !== 'undefined' && KINDERGESCHICHTEN_DATEN.length > 0) {
                const story = KINDERGESCHICHTEN_DATEN[0];
                storyList.innerHTML = `
                    <div class="story-container" style="
                        background: white;
                        border-radius: 16px;
                        padding: 0;
                        margin: 15px 0;
                        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
                        border: 1px solid #f0f0f0;
                        overflow: hidden;
                        display: flex;
                        min-height: 300px;
                    ">
                        <div class="story-cover" style="
                            flex: 0 0 30%;
                            background-image: url('bilder/kindergeschichten/Tennet und die magische Blume.png');
                            background-size: cover;
                            background-position: center;
                            background-repeat: no-repeat;
                        ">
                        </div>
                        <div class="story-content" style="
                            flex: 1;
                            padding: 25px;
                            display: flex;
                            flex-direction: column;
                            justify-content: space-between;
                        ">
                            <div class="story-text">
                                <h3 style="
                                    margin: 0 0 15px 0;
                                    font-size: 22px;
                                    font-weight: 700;
                                    color: #2c3e50;
                                    line-height: 1.3;
                                ">${story.titel}</h3>
                                <p style="
                                    margin: 0 0 20px 0;
                                    font-size: 14px;
                                    color: #555;
                                    line-height: 1.6;
                                ">${story.kurzbeschreibung}</p>
                            </div>
                            <div class="story-actions" style="
                                display: flex;
                                gap: 12px;
                                flex-wrap: wrap;
                            ">
                            <a href="#" onclick="downloadPDF('${story.pdfDateiname}'); return false;" style="
                                display: inline-flex;
                                align-items: center;
                                gap: 6px;
                                padding: 8px 16px;
                                background: #fff;
                                color: #333;
                                text-decoration: none;
                                border: 2px solid #ddd;
                                border-radius: 6px;
                                font-size: 13px;
                                font-weight: 500;
                                transition: all 0.3s ease;
                                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                                flex: 1;
                                justify-content: center;
                            " onmouseover="this.style.borderColor='#999'; this.style.boxShadow='0 4px 8px rgba(0,0,0,0.15)'"
                               onmouseout="this.style.borderColor='#ddd'; this.style.boxShadow='0 2px 4px rgba(0,0,0,0.1)'">
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                                    <polyline points="14,2 14,8 20,8"/>
                                    <line x1="16" y1="13" x2="8" y2="13"/>
                                    <line x1="16" y1="17" x2="8" y2="17"/>
                                    <polyline points="10,9 9,9 8,9"/>
                                </svg>
                                PDF herunterladen
                            </a>
                            <a href="#" onclick="playTennetStory(); return false;" style="
                                display: inline-flex;
                                align-items: center;
                                gap: 6px;
                                padding: 8px 16px;
                                background: #fff;
                                color: #333;
                                text-decoration: none;
                                border: 2px solid #ddd;
                                border-radius: 6px;
                                font-size: 13px;
                                font-weight: 500;
                                transition: all 0.3s ease;
                                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                                flex: 1;
                                justify-content: center;
                            " onmouseover="this.style.borderColor='#999'; this.style.boxShadow='0 4px 8px rgba(0,0,0,0.15)'"
                               onmouseout="this.style.borderColor='#ddd'; this.style.boxShadow='0 2px 4px rgba(0,0,0,0.1)'">
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"/>
                                    <path d="M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07"/>
                                </svg>
                                Audio anhören
                            </a>
                            </div>
                        </div>
                    </div>
                `;
                console.log('Story hinzugefügt:', story.titel);
            } else {
                console.error('Keine Stories gefunden oder Element nicht vorhanden');
                if (storyList) {
                    storyList.innerHTML = '<p style="padding: 20px; text-align: center; color: red;">Keine Geschichten gefunden.</p>';
                }
            }
        });

        // Einfache Download-Funktion
        function downloadPDF(filename) {
            console.log('Download PDF:', filename);
            const link = document.createElement('a');
            link.href = `PDFs/kindergeschichten/${filename}`;
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // Funktion zum Abspielen der Tennet-Geschichte
        function playTennetStory() {
            console.log('=== playTennetStory aufgerufen ===');

            // Debug-Informationen
            console.log('window.kindergeschichtenPlayer:', window.kindergeschichtenPlayer);
            console.log('KINDERGESCHICHTEN_DATEN:', typeof KINDERGESCHICHTEN_DATEN !== 'undefined' ? KINDERGESCHICHTEN_DATEN : 'nicht definiert');

            // Prüfen ob Player existiert
            if (!window.kindergeschichtenPlayer) {
                console.log('Player noch nicht initialisiert, warte 1 Sekunde...');
                setTimeout(() => {
                    console.log('Nach Wartezeit - window.kindergeschichtenPlayer:', window.kindergeschichtenPlayer);
                    if (window.kindergeschichtenPlayer) {
                        console.log('Player gefunden! Stories:', window.kindergeschichtenPlayer.stories);
                        if (window.kindergeschichtenPlayer.stories && window.kindergeschichtenPlayer.stories.length > 0) {
                            console.log('Starte Geschichte mit Index 0...');
                            window.kindergeschichtenPlayer.safeMobileStorySelection(0);
                        } else {
                            console.error('Player gefunden, aber keine Stories geladen');
                            alert('Der Player ist bereit, aber keine Geschichten wurden geladen.');
                        }
                    } else {
                        console.error('Player konnte auch nach Wartezeit nicht gefunden werden');
                        alert('Der Audio-Player konnte nicht initialisiert werden. Bitte laden Sie die Seite neu.');
                    }
                }, 1000);
                return;
            }

            // Player existiert - prüfen ob Geschichten geladen sind
            console.log('Player existiert. Stories:', window.kindergeschichtenPlayer.stories);
            if (!window.kindergeschichtenPlayer.stories || window.kindergeschichtenPlayer.stories.length === 0) {
                console.log('Geschichten noch nicht geladen, warte 1 Sekunde...');
                setTimeout(() => {
                    console.log('Nach Wartezeit - Stories:', window.kindergeschichtenPlayer.stories);
                    if (window.kindergeschichtenPlayer.stories && window.kindergeschichtenPlayer.stories.length > 0) {
                        console.log('Geschichten geladen, starte Geschichte...');
                        window.kindergeschichtenPlayer.safeMobileStorySelection(0);
                    } else {
                        console.error('Geschichten konnten auch nach Wartezeit nicht geladen werden');
                        alert('Die Geschichten konnten nicht geladen werden. Bitte laden Sie die Seite neu.');
                    }
                }, 1000);
                return;
            }

            // Alles bereit - Geschichte starten
            console.log('=== Alles bereit! Starte Tennet Geschichte mit Index 0 ===');
            console.log('Erste Geschichte:', window.kindergeschichtenPlayer.stories[0]);
            window.kindergeschichtenPlayer.safeMobileStorySelection(0);
        }
    </script>
    <script src="kindergeschichten-player.js"></script>
</body>
</html>
